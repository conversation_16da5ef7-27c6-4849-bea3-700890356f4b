const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');

const db = require('../config/database');
const redis = require('../config/redis');
const logger = require('../utils/logger');
const { sendOTP, verifyOTP } = require('../utils/sms');
const { generateTokens, verifyRefreshToken } = require('../utils/jwt');

const router = express.Router();

// Rate limiting for auth routes
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later.',
    message_ar: 'عدد كبير جداً من محاولات المصادقة، يرجى المحاولة لاحقاً.'
  }
});

// Register validation rules
const registerValidation = [
  body('phone')
    .matches(/^01[0-2,5]{1}[0-9]{8}$/)
    .withMessage('Invalid Egyptian phone number'),
  body('name')
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters'),
  body('password')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least 8 characters with uppercase, lowercase and number'),
  body('userType')
    .isIn(['customer', 'captain', 'store_owner'])
    .withMessage('Invalid user type')
];

// Login validation rules
const loginValidation = [
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  // Custom validation for phone or email
  body().custom((value, { req }) => {
    const { phone, email } = req.body;

    // Must have either phone or email
    if (!phone && !email) {
      throw new Error('Phone number or email is required');
    }

    // If phone is provided, validate it
    if (phone && !phone.match(/^01[0-2,5]{1}[0-9]{8}$/)) {
      throw new Error('Invalid Egyptian phone number');
    }

    // If email is provided, validate it
    if (email && !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      throw new Error('Invalid email format');
    }

    return true;
  })
];

// @route   POST /api/auth/register
// @desc    Register a new user
// @access  Public
router.post('/register', authLimiter, registerValidation, async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        message_ar: 'فشل في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const { phone, name, password, userType, email } = req.body;

    // Check if user already exists
    const existingUser = await db('users')
      .where('phone', phone)
      .orWhere('email', email)
      .first();

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User already exists with this phone or email',
        message_ar: 'المستخدم موجود بالفعل بهذا الهاتف أو البريد الإلكتروني'
      });
    }

    // Hash password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user
    const [userId] = await db('users').insert({
      phone,
      name,
      email,
      password: hashedPassword,
      user_type: userType,
      is_verified: false,
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    }).returning('id');

    // Generate OTP and send SMS
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Store OTP in Redis
    await redis.set(`otp_${phone}`, JSON.stringify({
      otp,
      userId: userId.id,
      expiresAt: otpExpiry.toISOString()
    }), 600); // 10 minutes

    // Send OTP via SMS
    try {
      await sendOTP(phone, otp);
    } catch (smsError) {
      logger.error('Failed to send OTP SMS:', smsError);
      // Continue without failing the registration
    }

    logger.info(`User registered: ${userId.id} - ${phone}`);

    res.status(201).json({
      success: true,
      message: 'User registered successfully. Please verify your phone number.',
      message_ar: 'تم تسجيل المستخدم بنجاح. يرجى التحقق من رقم هاتفك.',
      data: {
        userId: userId.id,
        phone,
        name,
        userType,
        isVerified: false
      }
    });

  } catch (error) {
    logger.logError(error, req);
    res.status(500).json({
      success: false,
      message: 'Registration failed',
      message_ar: 'فشل في التسجيل'
    });
  }
});

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', authLimiter, loginValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        message_ar: 'فشل في التحقق من البيانات',
        errors: errors.array()
      });
    }

    const { phone, email, password } = req.body;

    // Find user by phone or email
    let user;
    if (phone) {
      user = await db('users')
        .where('phone', phone)
        .first();
    } else if (email) {
      user = await db('users')
        .where('email', email)
        .first();
    }

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
        message_ar: 'بيانات اعتماد غير صحيحة'
      });
    }

    // Check if user is active
    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account has been deactivated',
        message_ar: 'تم إلغاء تفعيل الحساب'
      });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
        message_ar: 'بيانات اعتماد غير صحيحة'
      });
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user.id);

    // Store refresh token in Redis
    await redis.set(`refresh_${user.id}`, refreshToken, 7 * 24 * 60 * 60); // 7 days

    // Update last login
    await db('users')
      .where('id', user.id)
      .update({ last_login: new Date() });

    logger.info(`User logged in: ${user.id} - ${phone || email}`);

    res.json({
      success: true,
      message: 'Login successful',
      message_ar: 'تم تسجيل الدخول بنجاح',
      data: {
        user: {
          id: user.id,
          phone: user.phone,
          email: user.email,
          name: user.name,
          userType: user.user_type,
          isVerified: user.is_verified,
          avatar: user.avatar
        },
        tokens: {
          accessToken,
          refreshToken
        }
      }
    });

  } catch (error) {
    logger.logError(error, req);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      message_ar: 'فشل في تسجيل الدخول'
    });
  }
});

module.exports = router;
