# Trago - تطبيق توصيل الطلبات

## نظرة عامة
Trago هو تطبيق شامل لتوصيل الطلبات من المتاجر والمطاعم إلى منازل الزبائن، يتكون من ثلاثة مكونات رئيسية:

### 🧍‍♂️ تطبيق المستخدم (Flutter)
- تسجيل الدخول والتسجيل
- اختيار العنوان وإدارة العناوين
- استعراض المتاجر والمطاعم
- إضافة المنتجات إلى السلة
- الدفع أونلاين أو عند الاستلام
- تتبع الطلب في الوقت الفعلي
- نظام التقييمات والمراجعات
- دعم اللغتين العربية والإنجليزية

### 🚗 تطبيق الكابتن (Flutter)
- تسجيل حساب الكابتن والتحقق
- استقبال الطلبات الجديدة
- تتبع موقع الزبون باستخدام GPS
- إثبات التوصيل والتأكيد
- إدارة الحالة (متاح/غير متاح)
- عرض الإحصائيات والأرباح

### 🧑‍💼 لوحة تحكم الإدارة (React.js)
- إدارة المستخدمين والكباتن
- إدارة المتاجر والمطاعم
- مراقبة الطلبات في الوقت الفعلي
- إرسال التنبيهات والإشعارات
- مراجعة الإحصائيات والتقارير
- إدارة المدفوعات والعمولات

## التقنيات المستخدمة

| المكون | التقنية |
|--------|---------|
| Frontend Mobile | Flutter (Android & iOS) |
| Backend API | Node.js + Express |
| قاعدة البيانات | PostgreSQL + Redis |
| الموقع والتتبع | Google Maps API |
| الدفع | فودافون كاش، فوري، موبايل كاش، COD |
| لوحة التحكم | React.js |
| الإشعارات | Firebase Cloud Messaging |
| التخزين | AWS S3 / Cloudinary |

## الألوان والتصميم
- الألوان الأساسية: أصفر (#FFD700) وأسود (#000000)
- ألوان مساعدة: رمادي (#F5F5F5) وأبيض (#FFFFFF)
- تصميم متجاوب يدعم الوضع الليلي والنهاري

## هيكل المشروع
```
trago/
├── backend/                 # Node.js API Server
├── user-app/               # Flutter User App
├── captain-app/            # Flutter Captain App
├── admin-dashboard/        # React.js Admin Panel
├── shared/                 # Shared utilities and configs
└── docs/                   # Documentation
```

## المتطلبات
- Node.js 18+
- Flutter 3.0+
- PostgreSQL 14+
- Redis 6+
- Google Maps API Key
- Firebase Project

## التثبيت والتشغيل
سيتم إضافة تعليمات التثبيت لكل مكون في مجلداته الخاصة.

## الترخيص
جميع الحقوق محفوظة - Trago 2024
