const jwt = require('jsonwebtoken');
const logger = require('./logger');

// Generate access and refresh tokens
const generateTokens = (userId) => {
  try {
    const payload = { userId };
    
    const accessToken = jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { 
        expiresIn: process.env.JWT_EXPIRES_IN || '1h',
        issuer: 'trago-api',
        audience: 'trago-app'
      }
    );

    const refreshToken = jwt.sign(
      payload,
      process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET,
      { 
        expiresIn: '7d',
        issuer: 'trago-api',
        audience: 'trago-app'
      }
    );

    return { accessToken, refreshToken };
  } catch (error) {
    logger.error('Error generating tokens:', error);
    throw new Error('Token generation failed');
  }
};

// Verify access token
const verifyAccessToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET, {
      issuer: 'trago-api',
      audience: 'trago-app'
    });
  } catch (error) {
    logger.error('Error verifying access token:', error);
    throw error;
  }
};

// Verify refresh token
const verifyRefreshToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET, {
      issuer: 'trago-api',
      audience: 'trago-app'
    });
  } catch (error) {
    logger.error('Error verifying refresh token:', error);
    throw error;
  }
};

// Decode token without verification (for expired tokens)
const decodeToken = (token) => {
  try {
    return jwt.decode(token);
  } catch (error) {
    logger.error('Error decoding token:', error);
    return null;
  }
};

// Get token expiration time
const getTokenExpiration = (token) => {
  try {
    const decoded = jwt.decode(token);
    return decoded ? new Date(decoded.exp * 1000) : null;
  } catch (error) {
    logger.error('Error getting token expiration:', error);
    return null;
  }
};

// Check if token is expired
const isTokenExpired = (token) => {
  try {
    const expiration = getTokenExpiration(token);
    return expiration ? expiration < new Date() : true;
  } catch (error) {
    logger.error('Error checking token expiration:', error);
    return true;
  }
};

// Generate password reset token
const generatePasswordResetToken = (userId) => {
  try {
    return jwt.sign(
      { userId, type: 'password_reset' },
      process.env.JWT_SECRET,
      { 
        expiresIn: '1h',
        issuer: 'trago-api',
        audience: 'trago-app'
      }
    );
  } catch (error) {
    logger.error('Error generating password reset token:', error);
    throw new Error('Password reset token generation failed');
  }
};

// Verify password reset token
const verifyPasswordResetToken = (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET, {
      issuer: 'trago-api',
      audience: 'trago-app'
    });
    
    if (decoded.type !== 'password_reset') {
      throw new Error('Invalid token type');
    }
    
    return decoded;
  } catch (error) {
    logger.error('Error verifying password reset token:', error);
    throw error;
  }
};

// Generate email verification token
const generateEmailVerificationToken = (userId) => {
  try {
    return jwt.sign(
      { userId, type: 'email_verification' },
      process.env.JWT_SECRET,
      { 
        expiresIn: '24h',
        issuer: 'trago-api',
        audience: 'trago-app'
      }
    );
  } catch (error) {
    logger.error('Error generating email verification token:', error);
    throw new Error('Email verification token generation failed');
  }
};

// Verify email verification token
const verifyEmailVerificationToken = (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET, {
      issuer: 'trago-api',
      audience: 'trago-app'
    });
    
    if (decoded.type !== 'email_verification') {
      throw new Error('Invalid token type');
    }
    
    return decoded;
  } catch (error) {
    logger.error('Error verifying email verification token:', error);
    throw error;
  }
};

module.exports = {
  generateTokens,
  verifyAccessToken,
  verifyRefreshToken,
  decodeToken,
  getTokenExpiration,
  isTokenExpired,
  generatePasswordResetToken,
  verifyPasswordResetToken,
  generateEmailVerificationToken,
  verifyEmailVerificationToken
};
