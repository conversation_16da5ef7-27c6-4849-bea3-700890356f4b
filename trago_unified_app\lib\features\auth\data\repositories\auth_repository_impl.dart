import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../../../../core/models/user_model.dart';
import '../../../../core/utils/api_result.dart';
import '../../../../core/services/api_service.dart';
import '../../domain/repositories/auth_repository.dart';

class AuthRepository implements AuthRepositoryInterface {
  final ApiService _apiService;

  AuthRepository({ApiService? apiService}) 
      : _apiService = apiService ?? ApiService();

  @override
  Future<ApiResult<UserModel>> login({
    required String phone,
    required String password,
    required String userType,
  }) async {
    try {
      final response = await _apiService.post(
        '/auth/login',
        data: {
          'phone': phone,
          'password': password,
          'user_type': userType,
        },
      );

      if (response.data['success'] == true) {
        final userData = response.data['data']['user'] as Map<String, dynamic>;
        final user = UserModel.fromJson(userData);
        
        // Save access token
        final token = response.data['data']['token'] as String?;
        if (token != null) {
          await _apiService.setAuthToken(token);
        }

        return ApiResult.success(
          user,
          message: response.data['message'] ?? 'تم تسجيل الدخول بنجاح',
        );
      } else {
        return ApiResult.error(
          response.data['message'] ?? 'فشل في تسجيل الدخول',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      debugPrint('Login error: $e');
      return ApiResult.error('حدث خطأ غير متوقع');
    }
  }

  @override
  Future<ApiResult<void>> register({
    required String name,
    required String phone,
    required String email,
    required String password,
    required String userType,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final data = {
        'name': name,
        'phone': phone,
        'email': email,
        'password': password,
        'user_type': userType,
        ...?additionalData,
      };

      final response = await _apiService.post('/auth/register', data: data);

      if (response.data['success'] == true) {
        return ApiResult.success(
          null,
          message: response.data['message'] ?? 'تم إنشاء الحساب بنجاح',
        );
      } else {
        return ApiResult.error(
          response.data['message'] ?? 'فشل في إنشاء الحساب',
          statusCode: response.statusCode,
          errors: response.data['errors'],
        );
      }
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      debugPrint('Register error: $e');
      return ApiResult.error('حدث خطأ غير متوقع');
    }
  }

  @override
  Future<ApiResult<UserModel>> verifyOTP({
    required String phone,
    required String otp,
    required String userType,
  }) async {
    try {
      final response = await _apiService.post(
        '/auth/verify-otp',
        data: {
          'phone': phone,
          'otp': otp,
          'user_type': userType,
        },
      );

      if (response.data['success'] == true) {
        final userData = response.data['data']['user'] as Map<String, dynamic>;
        final user = UserModel.fromJson(userData);
        
        // Save access token
        final token = response.data['data']['token'] as String?;
        if (token != null) {
          await _apiService.setAuthToken(token);
        }

        return ApiResult.success(
          user,
          message: response.data['message'] ?? 'تم التحقق بنجاح',
        );
      } else {
        return ApiResult.error(
          response.data['message'] ?? 'فشل في التحقق من الرمز',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      debugPrint('Verify OTP error: $e');
      return ApiResult.error('حدث خطأ غير متوقع');
    }
  }

  @override
  Future<ApiResult<void>> resendOTP({
    required String phone,
    required String userType,
  }) async {
    try {
      final response = await _apiService.post(
        '/auth/resend-otp',
        data: {
          'phone': phone,
          'user_type': userType,
        },
      );

      if (response.data['success'] == true) {
        return ApiResult.success(
          null,
          message: response.data['message'] ?? 'تم إرسال الرمز مرة أخرى',
        );
      } else {
        return ApiResult.error(
          response.data['message'] ?? 'فشل في إرسال الرمز',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      debugPrint('Resend OTP error: $e');
      return ApiResult.error('حدث خطأ غير متوقع');
    }
  }

  @override
  Future<ApiResult<void>> forgotPassword({
    required String phone,
    required String userType,
  }) async {
    try {
      final response = await _apiService.post(
        '/auth/forgot-password',
        data: {
          'phone': phone,
          'user_type': userType,
        },
      );

      if (response.data['success'] == true) {
        return ApiResult.success(
          null,
          message: response.data['message'] ?? 'تم إرسال رمز استعادة كلمة المرور',
        );
      } else {
        return ApiResult.error(
          response.data['message'] ?? 'فشل في إرسال رمز الاستعادة',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      debugPrint('Forgot password error: $e');
      return ApiResult.error('حدث خطأ غير متوقع');
    }
  }

  @override
  Future<ApiResult<void>> resetPassword({
    required String phone,
    required String otp,
    required String newPassword,
    required String userType,
  }) async {
    try {
      final response = await _apiService.post(
        '/auth/reset-password',
        data: {
          'phone': phone,
          'otp': otp,
          'new_password': newPassword,
          'user_type': userType,
        },
      );

      if (response.data['success'] == true) {
        return ApiResult.success(
          null,
          message: response.data['message'] ?? 'تم تغيير كلمة المرور بنجاح',
        );
      } else {
        return ApiResult.error(
          response.data['message'] ?? 'فشل في تغيير كلمة المرور',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      debugPrint('Reset password error: $e');
      return ApiResult.error('حدث خطأ غير متوقع');
    }
  }

  @override
  Future<ApiResult<UserModel>> updateProfile({
    required String name,
    String? email,
    String? avatar,
  }) async {
    try {
      final data = <String, dynamic>{
        'name': name,
      };
      
      if (email != null) data['email'] = email;
      if (avatar != null) data['avatar'] = avatar;

      final response = await _apiService.put('/auth/profile', data: data);

      if (response.data['success'] == true) {
        final userData = response.data['data'] as Map<String, dynamic>;
        final user = UserModel.fromJson(userData);

        return ApiResult.success(
          user,
          message: response.data['message'] ?? 'تم تحديث الملف الشخصي بنجاح',
        );
      } else {
        return ApiResult.error(
          response.data['message'] ?? 'فشل في تحديث الملف الشخصي',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      debugPrint('Update profile error: $e');
      return ApiResult.error('حدث خطأ غير متوقع');
    }
  }

  @override
  Future<ApiResult<void>> logout() async {
    try {
      await _apiService.post('/auth/logout');
      await _apiService.clearAuthToken();
      
      return ApiResult.success(
        null,
        message: 'تم تسجيل الخروج بنجاح',
      );
    } on DioException catch (e) {
      // Even if logout fails on server, clear local token
      await _apiService.clearAuthToken();
      return _handleDioError(e);
    } catch (e) {
      await _apiService.clearAuthToken();
      debugPrint('Logout error: $e');
      return ApiResult.error('حدث خطأ أثناء تسجيل الخروج');
    }
  }

  ApiResult<T> _handleDioError<T>(DioException e) {
    String message;
    int? statusCode = e.response?.statusCode;

    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        message = 'انتهت مهلة الاتصال';
        break;
      case DioExceptionType.badResponse:
        final responseData = e.response?.data;
        if (responseData is Map<String, dynamic>) {
          message = responseData['message'] ?? 'حدث خطأ في الخادم';
        } else {
          message = 'حدث خطأ في الخادم';
        }
        break;
      case DioExceptionType.cancel:
        message = 'تم إلغاء الطلب';
        break;
      case DioExceptionType.connectionError:
        message = 'لا يوجد اتصال بالإنترنت';
        break;
      default:
        message = 'حدث خطأ غير متوقع';
    }

    return ApiResult.error(message, statusCode: statusCode);
  }
}
