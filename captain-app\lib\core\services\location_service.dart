import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import '../config/app_config.dart';
import 'api_service.dart';
import 'storage_service.dart';

class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  final ApiService _apiService = ApiService();
  final StorageService _storage = StorageService();

  StreamSubscription<Position>? _positionStream;
  Timer? _locationUpdateTimer;
  Position? _currentPosition;
  bool _isTracking = false;

  // Stream controllers
  final StreamController<Position> _positionController = StreamController<Position>.broadcast();
  final StreamController<bool> _trackingStatusController = StreamController<bool>.broadcast();

  // Getters
  Stream<Position> get positionStream => _positionController.stream;
  Stream<bool> get trackingStatusStream => _trackingStatusController.stream;
  Position? get currentPosition => _currentPosition;
  bool get isTracking => _isTracking;

  // Initialize location service
  Future<void> initialize() async {
    await _checkPermissions();
    await _loadLastKnownLocation();
  }

  // Check and request location permissions
  Future<bool> _checkPermissions() async {
    // Check if location services are enabled
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return false;
    }

    // Check location permission
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return false;
    }

    await _storage.setLocationPermissionGranted(true);
    return true;
  }

  // Request location permissions
  Future<bool> requestPermissions() async {
    try {
      // Request location permission using permission_handler
      final status = await Permission.location.request();
      
      if (status.isGranted) {
        await _storage.setLocationPermissionGranted(true);
        return true;
      } else if (status.isPermanentlyDenied) {
        // Open app settings
        await openAppSettings();
        return false;
      }
      
      return false;
    } catch (e) {
      return false;
    }
  }

  // Check if permissions are granted
  Future<bool> hasPermissions() async {
    final serviceEnabled = await Geolocator.isLocationServiceEnabled();
    final permission = await Geolocator.checkPermission();
    
    return serviceEnabled && 
           (permission == LocationPermission.always || 
            permission == LocationPermission.whileInUse);
  }

  // Get current location once
  Future<Position?> getCurrentLocation() async {
    try {
      if (!await hasPermissions()) {
        final granted = await requestPermissions();
        if (!granted) return null;
      }

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      _currentPosition = position;
      _positionController.add(position);
      
      // Save to storage
      await _storage.saveLastLocation(
        latitude: position.latitude,
        longitude: position.longitude,
      );

      return position;
    } catch (e) {
      return null;
    }
  }

  // Start location tracking
  Future<bool> startTracking() async {
    if (_isTracking) return true;

    try {
      if (!await hasPermissions()) {
        final granted = await requestPermissions();
        if (!granted) return false;
      }

      const locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
      );

      _positionStream = Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen(
        (Position position) {
          _currentPosition = position;
          _positionController.add(position);
          _onLocationUpdate(position);
        },
        onError: (error) {
          _handleLocationError(error);
        },
      );

      // Start periodic API updates
      _startPeriodicUpdates();

      _isTracking = true;
      _trackingStatusController.add(true);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Stop location tracking
  Future<void> stopTracking() async {
    if (!_isTracking) return;

    await _positionStream?.cancel();
    _positionStream = null;
    
    _locationUpdateTimer?.cancel();
    _locationUpdateTimer = null;

    _isTracking = false;
    _trackingStatusController.add(false);
  }

  // Start periodic location updates to server
  void _startPeriodicUpdates() {
    _locationUpdateTimer = Timer.periodic(
      Duration(seconds: AppConfig.locationUpdateInterval ~/ 1000),
      (timer) {
        if (_currentPosition != null) {
          _updateLocationOnServer(_currentPosition!);
        }
      },
    );
  }

  // Handle location updates
  void _onLocationUpdate(Position position) async {
    // Save to local storage
    await _storage.saveLastLocation(
      latitude: position.latitude,
      longitude: position.longitude,
    );
  }

  // Update location on server
  Future<void> _updateLocationOnServer(Position position) async {
    try {
      await _apiService.updateLocation(
        latitude: position.latitude,
        longitude: position.longitude,
      );
    } catch (e) {
      // Handle error silently - location updates should not interrupt the app
    }
  }

  // Load last known location from storage
  Future<void> _loadLastKnownLocation() async {
    final lastLocation = await _storage.getLastLocation();
    if (lastLocation != null) {
      _currentPosition = Position(
        latitude: lastLocation['latitude']!,
        longitude: lastLocation['longitude']!,
        timestamp: await _storage.getLastLocationTimestamp() ?? DateTime.now(),
        accuracy: 0,
        altitude: 0,
        heading: 0,
        speed: 0,
        speedAccuracy: 0,
        altitudeAccuracy: 0,
        headingAccuracy: 0,
      );
    }
  }

  // Handle location errors
  void _handleLocationError(dynamic error) {
    if (error is LocationServiceDisabledException) {
      // Location services are disabled
    } else if (error is PermissionDeniedException) {
      // Permission denied
      _storage.setLocationPermissionGranted(false);
    }
  }

  // Calculate distance between two points
  double calculateDistance({
    required double startLatitude,
    required double startLongitude,
    required double endLatitude,
    required double endLongitude,
  }) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  // Calculate bearing between two points
  double calculateBearing({
    required double startLatitude,
    required double startLongitude,
    required double endLatitude,
    required double endLongitude,
  }) {
    return Geolocator.bearingBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  // Check if captain is within delivery range
  bool isWithinDeliveryRange({
    required double targetLatitude,
    required double targetLongitude,
    double maxDistance = 50000, // 50km default
  }) {
    if (_currentPosition == null) return false;

    final distance = calculateDistance(
      startLatitude: _currentPosition!.latitude,
      startLongitude: _currentPosition!.longitude,
      endLatitude: targetLatitude,
      endLongitude: targetLongitude,
    );

    return distance <= maxDistance;
  }

  // Get location accuracy status
  String getLocationAccuracyStatus() {
    if (_currentPosition == null) return 'Unknown';
    
    final accuracy = _currentPosition!.accuracy;
    if (accuracy <= 5) return 'Excellent';
    if (accuracy <= 10) return 'Good';
    if (accuracy <= 20) return 'Fair';
    return 'Poor';
  }

  // Dispose resources
  void dispose() {
    _positionStream?.cancel();
    _locationUpdateTimer?.cancel();
    _positionController.close();
    _trackingStatusController.close();
  }
}
