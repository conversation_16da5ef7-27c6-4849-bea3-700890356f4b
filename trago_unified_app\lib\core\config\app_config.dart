import 'package:flutter/material.dart';

class AppConfig {
  // App Information
  static const String appName = 'Trago';
  static const String appNameArabic = 'تراجو';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق توصيل الطلبات الموحد';
  static const String appDescriptionEn = 'Unified Food & Store Delivery App';

  // API Configuration
  static const String baseUrl = 'https://api.trago-app.com';
  static const String apiVersion = 'v1';
  static const String apiUrl = '$baseUrl/api';
  
  // Socket Configuration
  static const String socketUrl = 'https://api.trago-app.com';
  
  // Supported Languages
  static const List<Locale> supportedLocales = [
    Locale('ar'),
    Locale('en'),
  ];
  
  // Default Settings
  static const Locale defaultLocale = Locale('ar');
  static const ThemeMode defaultThemeMode = ThemeMode.light;
  
  // Timeouts
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // Cache Durations
  static const Duration shortCacheDuration = Duration(minutes: 5);
  static const Duration mediumCacheDuration = Duration(minutes: 30);
  static const Duration longCacheDuration = Duration(hours: 24);
  
  // Validation Rules
  static const int phoneMinLength = 11;
  static const int phoneMaxLength = 11;
  static const int passwordMinLength = 8;
  static const int passwordMaxLength = 50;
  static const int nameMinLength = 2;
  static const int nameMaxLength = 50;
  static const int otpLength = 6;
  
  // Regular Expressions
  static const String phoneRegex = r'^01[0-2,5]{1}[0-9]{8}$';
  static const String emailRegex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String passwordRegex = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$';
  static const String otpRegex = r'^\d{6}$';
  
  // File Upload Limits
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int maxImagesCount = 5;
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  
  // Map Configuration
  static const double defaultLatitude = 30.0444; // Cairo
  static const double defaultLongitude = 31.2357; // Cairo
  static const double minZoomLevel = 10.0;
  static const double maxZoomLevel = 20.0;
  static const double defaultZoomLevel = 15.0;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;
  
  // Feature Flags
  static const bool enableBiometricAuth = true;
  static const bool enableDarkMode = true;
  static const bool enableNotifications = true;
  static const bool enableLocationTracking = true;
  static const bool enableCrashReporting = true;
  static const bool enableAnalytics = true;
  
  // User Types
  static const String userTypeCustomer = 'customer';
  static const String userTypeCaptain = 'captain';
  static const String userTypeStoreOwner = 'store_owner';
  static const String userTypeAdmin = 'admin';
  
  // Vehicle Types (for captains)
  static const String vehicleTypeBicycle = 'bicycle';
  static const String vehicleTypeMotorcycle = 'motorcycle';
  static const String vehicleTypeCar = 'car';
  static const String vehicleTypeWalking = 'walking';
  
  // Payment Methods
  static const String paymentMethodCash = 'cash';
  static const String paymentMethodVodafoneCash = 'vodafone_cash';
  static const String paymentMethodFawry = 'fawry';
  static const String paymentMethodOrangeCash = 'orange_cash';
  static const String paymentMethodEtisalatCash = 'etisalat_cash';
  
  // Order Status
  static const String orderStatusPending = 'pending';
  static const String orderStatusConfirmed = 'confirmed';
  static const String orderStatusPreparing = 'preparing';
  static const String orderStatusReady = 'ready';
  static const String orderStatusPickedUp = 'picked_up';
  static const String orderStatusOnTheWay = 'on_the_way';
  static const String orderStatusDelivered = 'delivered';
  static const String orderStatusCancelled = 'cancelled';
  
  // Captain Status
  static const String captainStatusOffline = 'offline';
  static const String captainStatusOnline = 'online';
  static const String captainStatusBusy = 'busy';
}
