import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF2E7D32); // Green
  static const Color primaryLight = Color(0xFF4CAF50);
  static const Color primaryDark = Color(0xFF1B5E20);
  static const Color primaryVariant = Color(0xFF66BB6A);

  // Secondary Colors
  static const Color secondary = Color(0xFFFF9800); // Orange
  static const Color secondaryLight = Color(0xFFFFB74D);
  static const Color secondaryDark = Color(0xFFE65100);
  static const Color secondaryVariant = Color(0xFFFFCC02);

  // Background Colors
  static const Color background = Color(0xFFFAFAFA);
  static const Color backgroundDark = Color(0xFF121212);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF1E1E1E);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  static const Color surfaceVariantDark = Color(0xFF2C2C2C);

  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textPrimaryDark = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textSecondaryDark = Color(0xFFB3B3B3);
  static const Color textHint = Color(0xFF9E9E9E);
  static const Color textHintDark = Color(0xFF666666);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textOnSecondary = Color(0xFF000000);

  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color successLight = Color(0xFF81C784);
  static const Color successDark = Color(0xFF2E7D32);

  static const Color warning = Color(0xFFFF9800);
  static const Color warningLight = Color(0xFFFFB74D);
  static const Color warningDark = Color(0xFFE65100);

  static const Color error = Color(0xFFF44336);
  static const Color errorLight = Color(0xFFE57373);
  static const Color errorDark = Color(0xFFD32F2F);

  static const Color info = Color(0xFF2196F3);
  static const Color infoLight = Color(0xFF64B5F6);
  static const Color infoDark = Color(0xFF1976D2);

  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderDark = Color(0xFF424242);
  static const Color borderLight = Color(0xFFF5F5F5);

  // Divider Colors
  static const Color divider = Color(0xFFBDBDBD);
  static const Color dividerDark = Color(0xFF424242);

  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowDark = Color(0x33000000);

  // Overlay Colors
  static const Color overlay = Color(0x80000000);
  static const Color overlayLight = Color(0x40000000);

  // Captain Specific Colors
  static const Color captainPrimary = Color(0xFF1976D2); // Blue
  static const Color captainSecondary = Color(0xFF03DAC6); // Teal
  static const Color captainAccent = Color(0xFFFF6F00); // Deep Orange

  // Customer Specific Colors
  static const Color customerPrimary = Color(0xFF2E7D32); // Green
  static const Color customerSecondary = Color(0xFFFF9800); // Orange
  static const Color customerAccent = Color(0xFF9C27B0); // Purple

  // Order Status Colors
  static const Color orderPending = Color(0xFFFF9800); // Orange
  static const Color orderConfirmed = Color(0xFF2196F3); // Blue
  static const Color orderPreparing = Color(0xFFFF5722); // Deep Orange
  static const Color orderReady = Color(0xFF9C27B0); // Purple
  static const Color orderPickedUp = Color(0xFF607D8B); // Blue Grey
  static const Color orderOnTheWay = Color(0xFF3F51B5); // Indigo
  static const Color orderDelivered = Color(0xFF4CAF50); // Green
  static const Color orderCancelled = Color(0xFFF44336); // Red

  // Rating Colors
  static const Color ratingExcellent = Color(0xFF4CAF50); // Green
  static const Color ratingGood = Color(0xFF8BC34A); // Light Green
  static const Color ratingAverage = Color(0xFFFF9800); // Orange
  static const Color ratingPoor = Color(0xFFFF5722); // Deep Orange
  static const Color ratingBad = Color(0xFFF44336); // Red

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient captainGradient = LinearGradient(
    colors: [captainPrimary, captainSecondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient customerGradient = LinearGradient(
    colors: [customerPrimary, customerSecondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Shimmer Colors
  static const Color shimmerBase = Color(0xFFE0E0E0);
  static const Color shimmerHighlight = Color(0xFFF5F5F5);
  static const Color shimmerBaseDark = Color(0xFF424242);
  static const Color shimmerHighlightDark = Color(0xFF616161);

  // Helper Methods
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }

  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }
}
