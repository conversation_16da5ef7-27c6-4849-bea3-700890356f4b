import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';

class StorageService {
  static late SharedPreferences _prefs;
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Storage Keys
  static const String _keyAccessToken = 'access_token';
  static const String _keyRefreshToken = 'refresh_token';
  static const String _keyUserData = 'user_data';
  static const String _keyUserType = 'user_type';
  static const String _keyIsLoggedIn = 'is_logged_in';
  static const String _keyOnboardingCompleted = 'onboarding_completed';
  static const String _keyLanguage = 'language';
  static const String _keyThemeMode = 'theme_mode';
  static const String _keyNotificationsEnabled = 'notifications_enabled';
  static const String _keyLocationPermissionGranted = 'location_permission_granted';
  static const String _keyBiometricEnabled = 'biometric_enabled';
  static const String _keyLastLoginTime = 'last_login_time';
  static const String _keyAppVersion = 'app_version';

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Token Management
  static Future<void> saveAccessToken(String token) async {
    await _secureStorage.write(key: _keyAccessToken, value: token);
  }

  static Future<String?> getAccessToken() async {
    return await _secureStorage.read(key: _keyAccessToken);
  }

  static Future<void> saveRefreshToken(String token) async {
    await _secureStorage.write(key: _keyRefreshToken, value: token);
  }

  static Future<String?> getRefreshToken() async {
    return await _secureStorage.read(key: _keyRefreshToken);
  }

  static Future<void> clearTokens() async {
    await _secureStorage.delete(key: _keyAccessToken);
    await _secureStorage.delete(key: _keyRefreshToken);
  }

  // User Data Management
  static Future<void> saveUserData(UserModel user) async {
    final userJson = jsonEncode(user.toJson());
    await _prefs.setString(_keyUserData, userJson);
    await _prefs.setString(_keyUserType, user.userType);
    await _prefs.setBool(_keyIsLoggedIn, true);
    await _prefs.setString(_keyLastLoginTime, DateTime.now().toIso8601String());
  }

  static Future<UserModel?> getUserData() async {
    final userJson = _prefs.getString(_keyUserData);
    if (userJson != null) {
      try {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return UserModel.fromJson(userMap);
      } catch (e) {
        // Handle parsing error
        return null;
      }
    }
    return null;
  }

  static Future<String?> getUserType() async {
    return _prefs.getString(_keyUserType);
  }

  static Future<bool> isUserLoggedIn() async {
    final isLoggedIn = _prefs.getBool(_keyIsLoggedIn) ?? false;
    final token = await getAccessToken();
    return isLoggedIn && token != null;
  }

  static Future<void> clearUserData() async {
    await _prefs.remove(_keyUserData);
    await _prefs.remove(_keyUserType);
    await _prefs.setBool(_keyIsLoggedIn, false);
    await _prefs.remove(_keyLastLoginTime);
    await clearTokens();
  }

  // App Settings
  static Future<void> setOnboardingCompleted(bool completed) async {
    await _prefs.setBool(_keyOnboardingCompleted, completed);
  }

  static bool isOnboardingCompleted() {
    return _prefs.getBool(_keyOnboardingCompleted) ?? false;
  }

  static Future<void> setLanguage(String languageCode) async {
    await _prefs.setString(_keyLanguage, languageCode);
  }

  static String getLanguage() {
    return _prefs.getString(_keyLanguage) ?? 'ar';
  }

  static Future<void> setThemeMode(String themeMode) async {
    await _prefs.setString(_keyThemeMode, themeMode);
  }

  static String getThemeMode() {
    return _prefs.getString(_keyThemeMode) ?? 'light';
  }

  static Future<void> setNotificationsEnabled(bool enabled) async {
    await _prefs.setBool(_keyNotificationsEnabled, enabled);
  }

  static bool isNotificationsEnabled() {
    return _prefs.getBool(_keyNotificationsEnabled) ?? true;
  }

  static Future<void> setLocationPermissionGranted(bool granted) async {
    await _prefs.setBool(_keyLocationPermissionGranted, granted);
  }

  static bool isLocationPermissionGranted() {
    return _prefs.getBool(_keyLocationPermissionGranted) ?? false;
  }

  static Future<void> setBiometricEnabled(bool enabled) async {
    await _prefs.setBool(_keyBiometricEnabled, enabled);
  }

  static bool isBiometricEnabled() {
    return _prefs.getBool(_keyBiometricEnabled) ?? false;
  }

  static Future<void> setAppVersion(String version) async {
    await _prefs.setString(_keyAppVersion, version);
  }

  static String? getAppVersion() {
    return _prefs.getString(_keyAppVersion);
  }

  static DateTime? getLastLoginTime() {
    final timeString = _prefs.getString(_keyLastLoginTime);
    if (timeString != null) {
      try {
        return DateTime.parse(timeString);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Generic Methods
  static Future<void> setString(String key, String value) async {
    await _prefs.setString(key, value);
  }

  static String? getString(String key) {
    return _prefs.getString(key);
  }

  static Future<void> setBool(String key, bool value) async {
    await _prefs.setBool(key, value);
  }

  static bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  static Future<void> setInt(String key, int value) async {
    await _prefs.setInt(key, value);
  }

  static int? getInt(String key) {
    return _prefs.getInt(key);
  }

  static Future<void> setDouble(String key, double value) async {
    await _prefs.setDouble(key, value);
  }

  static double? getDouble(String key) {
    return _prefs.getDouble(key);
  }

  static Future<void> setStringList(String key, List<String> value) async {
    await _prefs.setStringList(key, value);
  }

  static List<String>? getStringList(String key) {
    return _prefs.getStringList(key);
  }

  static Future<void> remove(String key) async {
    await _prefs.remove(key);
  }

  static Future<void> clear() async {
    await _prefs.clear();
    await _secureStorage.deleteAll();
  }

  static bool containsKey(String key) {
    return _prefs.containsKey(key);
  }

  static Set<String> getKeys() {
    return _prefs.getKeys();
  }
}
