// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Trago';

  @override
  String get welcome => 'Welcome';

  @override
  String get login => 'Login';

  @override
  String get register => 'Register';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get password => 'Password';

  @override
  String get email => 'Email';

  @override
  String get name => 'Name';

  @override
  String get customer => 'Customer';

  @override
  String get captain => 'Captain';

  @override
  String get home => 'Home';

  @override
  String get orders => 'Orders';

  @override
  String get profile => 'Profile';

  @override
  String get settings => 'Settings';

  @override
  String get stores => 'Stores';

  @override
  String get cart => 'Cart';

  @override
  String get search => 'Search';

  @override
  String get earnings => 'Earnings';

  @override
  String get notifications => 'Notifications';

  @override
  String get logout => 'Logout';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get acceptTerms => 'I agree to the Terms and Conditions';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get createAccount => 'Create Account';

  @override
  String get verifyPhone => 'Verify Phone Number';

  @override
  String get enterOtpCode => 'Enter the code sent to';

  @override
  String get resendCode => 'Resend Code';

  @override
  String get verify => 'Verify';

  @override
  String get vehicleType => 'Vehicle Type';

  @override
  String get vehicleNumber => 'Vehicle Number';

  @override
  String get motorcycle => 'Motorcycle';

  @override
  String get car => 'Car';

  @override
  String get bicycle => 'Bicycle';

  @override
  String get walking => 'Walking';

  @override
  String get online => 'Online';

  @override
  String get offline => 'Offline';

  @override
  String get busy => 'Busy';
}
