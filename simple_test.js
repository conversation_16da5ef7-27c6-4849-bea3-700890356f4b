const http = require('http');

console.log('🎯 Testing Test Accounts');
console.log('========================');

// Test accounts
const accounts = [
  { name: 'Customer', email: '<EMAIL>', password: 'Test123456' },
  { name: 'Captain', email: '<EMAIL>', password: 'Test123456' },
  { name: 'Admin', email: '<EMAIL>', password: 'password123' }
];

function testLogin(account) {
  return new Promise((resolve) => {
    const data = JSON.stringify({
      email: account.email,
      password: account.password
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          if (result.success) {
            console.log(`✅ ${account.name}: Login successful`);
            console.log(`   Email: ${account.email}`);
            console.log(`   User: ${result.data.user.name}`);
            console.log(`   Type: ${result.data.user.userType}`);
          } else {
            console.log(`❌ ${account.name}: ${result.message}`);
          }
        } catch (e) {
          console.log(`❌ ${account.name}: Invalid response`);
        }
        resolve();
      });
    });

    req.on('error', (error) => {
      console.log(`❌ ${account.name}: Connection failed - ${error.message}`);
      resolve();
    });

    req.write(data);
    req.end();
  });
}

async function runTests() {
  console.log('Testing login for all accounts...\n');
  
  for (const account of accounts) {
    await testLogin(account);
    console.log('');
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('🎉 Test completed!');
  console.log('\nIf all accounts logged in successfully, you can use them in your apps:');
  console.log('- User App: <EMAIL> / Test123456');
  console.log('- Captain App: <EMAIL> / Test123456');
  console.log('- Admin Dashboard: <EMAIL> / password123');
}

runTests().catch(console.error);
