const bcrypt = require('bcryptjs');

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> } 
 */
exports.seed = async function(knex) {
  // Delete existing test users
  await knex('captains').whereIn('user_id', function() {
    this.select('id').from('users').whereIn('email', [
      '<EMAIL>',
      '<EMAIL>'
    ]);
  }).del();
  
  await knex('users').whereIn('email', [
    '<EMAIL>',
    '<EMAIL>'
  ]).del();
  
  // Hash the default password
  const hashedPassword = await bcrypt.hash('Test123456', 12);
  
  // Insert test users
  const testUsers = await knex('users').insert([
    {
      id: knex.raw("(lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))"),
      phone: '***********',
      email: '<EMAIL>',
      name: 'عميل تجريبي',
      password: hashedPassword,
      user_type: 'customer',
      is_verified: true,
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: knex.raw("(lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))"),
      phone: '***********',
      email: '<EMAIL>',
      name: 'كابتن تجريبي',
      password: hashedPassword,
      user_type: 'captain',
      is_verified: true,
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    }
  ]).returning(['id', 'email', 'user_type']);
  
  // Find the captain user to create captain profile
  const captainUser = await knex('users')
    .where('email', '<EMAIL>')
    .first();
  
  if (captainUser) {
    // Create captain profile
    await knex('captains').insert({
      id: knex.raw("(lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))"),
      user_id: captainUser.id,
      license_number: 'TEST123456',
      vehicle_type: 'motorcycle',
      vehicle_plate: 'ABC 1234',
      vehicle_model: 'Honda CBR',
      vehicle_color: 'أحمر',
      is_approved: true,
      status: 'offline',
      rating: 4.5,
      total_orders: 0,
      completed_orders: 0,
      cancelled_orders: 0,
      total_earnings: 0,
      approved_at: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    });
  }
  
  console.log('\n🎯 Test users created successfully!');
  console.log('==========================================');
  console.log('\n👤 Customer Account (User App):');
  console.log('📧 Email: <EMAIL>');
  console.log('📱 Phone: ***********');
  console.log('🔑 Password: Test123456');
  console.log('\n🏍️ Captain Account (Captain App):');
  console.log('📧 Email: <EMAIL>');
  console.log('📱 Phone: ***********');
  console.log('🔑 Password: Test123456');
  console.log('✅ Captain Status: Approved');
  console.log('\n💡 Use these accounts to test both apps!');
  console.log('==========================================\n');
};
