{"appName": "Trag<PERSON>", "appNameArabic": "تراجو", "colors": {"primary": {"main": "#FFD700", "light": "#FFED4E", "dark": "#B8860B", "contrastText": "#000000"}, "secondary": {"main": "#000000", "light": "#333333", "dark": "#000000", "contrastText": "#FFFFFF"}, "background": {"default": "#FFFFFF", "paper": "#F5F5F5", "dark": "#121212"}, "surface": {"main": "#FFFFFF", "elevated": "#F8F8F8", "dark": "#1E1E1E"}, "text": {"primary": "#000000", "secondary": "#666666", "disabled": "#999999", "hint": "#CCCCCC"}, "status": {"success": "#4CAF50", "warning": "#FF9800", "error": "#F44336", "info": "#2196F3"}, "order": {"pending": "#FF9800", "confirmed": "#2196F3", "preparing": "#9C27B0", "ready": "#4CAF50", "pickedUp": "#00BCD4", "onTheWay": "#3F51B5", "delivered": "#4CAF50", "cancelled": "#F44336"}}, "typography": {"fontFamily": {"primary": "Cairo", "secondary": "Roboto", "english": "Roboto", "arabic": "Cairo"}, "fontSize": {"xs": 12, "sm": 14, "base": 16, "lg": 18, "xl": 20, "2xl": 24, "3xl": 30, "4xl": 36}, "fontWeight": {"light": 300, "normal": 400, "medium": 500, "semibold": 600, "bold": 700, "extrabold": 800}}, "spacing": {"xs": 4, "sm": 8, "md": 16, "lg": 24, "xl": 32, "2xl": 48, "3xl": 64}, "borderRadius": {"none": 0, "sm": 4, "md": 8, "lg": 12, "xl": 16, "2xl": 24, "full": 9999}, "shadows": {"none": "none", "sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1)", "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1)"}, "components": {"button": {"primary": {"backgroundColor": "#FFD700", "color": "#000000", "borderRadius": 8, "padding": "12px 24px", "fontSize": 16, "fontWeight": 600}, "secondary": {"backgroundColor": "#000000", "color": "#FFFFFF", "borderRadius": 8, "padding": "12px 24px", "fontSize": 16, "fontWeight": 600}, "outline": {"backgroundColor": "transparent", "color": "#FFD700", "borderColor": "#FFD700", "borderWidth": 2, "borderRadius": 8, "padding": "12px 24px", "fontSize": 16, "fontWeight": 600}}, "card": {"backgroundColor": "#FFFFFF", "borderRadius": 12, "padding": 16, "shadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)"}, "input": {"backgroundColor": "#F5F5F5", "borderRadius": 8, "padding": "12px 16px", "fontSize": 16, "borderColor": "#E0E0E0", "focusBorderColor": "#FFD700"}}, "icons": {"size": {"xs": 16, "sm": 20, "md": 24, "lg": 32, "xl": 48}}, "breakpoints": {"mobile": 480, "tablet": 768, "desktop": 1024, "wide": 1280}, "animations": {"duration": {"fast": 150, "normal": 300, "slow": 500}, "easing": {"easeIn": "cubic-bezier(0.4, 0, 1, 1)", "easeOut": "cubic-bezier(0, 0, 0.2, 1)", "easeInOut": "cubic-bezier(0.4, 0, 0.2, 1)"}}, "localization": {"supportedLanguages": ["ar", "en"], "defaultLanguage": "ar", "rtlLanguages": ["ar"]}}