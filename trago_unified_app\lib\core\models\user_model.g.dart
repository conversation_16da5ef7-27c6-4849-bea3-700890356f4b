// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
      id: json['id'] as String,
      phone: json['phone'] as String,
      email: json['email'] as String?,
      name: json['name'] as String,
      avatar: json['avatar'] as String?,
      userType: json['user_type'] as String,
      isVerified: json['is_verified'] as bool,
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      lastLogin: json['last_login'] == null
          ? null
          : DateTime.parse(json['last_login'] as String),
      captainData: json['captain_data'] == null
          ? null
          : CaptainData.fromJson(json['captain_data'] as Map<String, dynamic>),
      customerData: json['customer_data'] == null
          ? null
          : CustomerData.fromJson(json['customer_data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
      'id': instance.id,
      'phone': instance.phone,
      'email': instance.email,
      'name': instance.name,
      'avatar': instance.avatar,
      'user_type': instance.userType,
      'is_verified': instance.isVerified,
      'is_active': instance.isActive,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'last_login': instance.lastLogin?.toIso8601String(),
      'captain_data': instance.captainData,
      'customer_data': instance.customerData,
    };

CaptainData _$CaptainDataFromJson(Map<String, dynamic> json) => CaptainData(
      licenseNumber: json['license_number'] as String?,
      vehicleType: json['vehicle_type'] as String,
      vehiclePlate: json['vehicle_plate'] as String?,
      vehicleModel: json['vehicle_model'] as String?,
      vehicleColor: json['vehicle_color'] as String?,
      isApproved: json['is_approved'] as bool,
      status: json['status'] as String,
      currentLocation: json['current_location'] == null
          ? null
          : LocationData.fromJson(json['current_location'] as Map<String, dynamic>),
      rating: (json['rating'] as num).toDouble(),
      totalOrders: json['total_orders'] as int,
      completedOrders: json['completed_orders'] as int,
      cancelledOrders: json['cancelled_orders'] as int,
      totalEarnings: (json['total_earnings'] as num).toDouble(),
      documents: (json['documents'] as List<dynamic>?)?.map((e) => e as String).toList(),
      approvedAt: json['approved_at'] == null
          ? null
          : DateTime.parse(json['approved_at'] as String),
    );

Map<String, dynamic> _$CaptainDataToJson(CaptainData instance) => <String, dynamic>{
      'license_number': instance.licenseNumber,
      'vehicle_type': instance.vehicleType,
      'vehicle_plate': instance.vehiclePlate,
      'vehicle_model': instance.vehicleModel,
      'vehicle_color': instance.vehicleColor,
      'is_approved': instance.isApproved,
      'status': instance.status,
      'current_location': instance.currentLocation,
      'rating': instance.rating,
      'total_orders': instance.totalOrders,
      'completed_orders': instance.completedOrders,
      'cancelled_orders': instance.cancelledOrders,
      'total_earnings': instance.totalEarnings,
      'documents': instance.documents,
      'approved_at': instance.approvedAt?.toIso8601String(),
    };

CustomerData _$CustomerDataFromJson(Map<String, dynamic> json) => CustomerData(
      deliveryAddresses: (json['delivery_addresses'] as List<dynamic>?)
          ?.map((e) => AddressData.fromJson(e as Map<String, dynamic>))
          .toList(),
      favoriteStores: (json['favorite_stores'] as List<dynamic>?)?.map((e) => e as String).toList(),
      totalOrders: json['total_orders'] as int,
      totalSpent: (json['total_spent'] as num).toDouble(),
      loyaltyPoints: json['loyalty_points'] as int,
      preferredPaymentMethod: json['preferred_payment_method'] as String?,
    );

Map<String, dynamic> _$CustomerDataToJson(CustomerData instance) => <String, dynamic>{
      'delivery_addresses': instance.deliveryAddresses,
      'favorite_stores': instance.favoriteStores,
      'total_orders': instance.totalOrders,
      'total_spent': instance.totalSpent,
      'loyalty_points': instance.loyaltyPoints,
      'preferred_payment_method': instance.preferredPaymentMethod,
    };

LocationData _$LocationDataFromJson(Map<String, dynamic> json) => LocationData(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String?,
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$LocationDataToJson(LocationData instance) => <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'address': instance.address,
      'updated_at': instance.updatedAt.toIso8601String(),
    };

AddressData _$AddressDataFromJson(Map<String, dynamic> json) => AddressData(
      id: json['id'] as String,
      title: json['title'] as String,
      address: json['address'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      isDefault: json['is_default'] as bool,
    );

Map<String, dynamic> _$AddressDataToJson(AddressData instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'address': instance.address,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'is_default': instance.isDefault,
    };
