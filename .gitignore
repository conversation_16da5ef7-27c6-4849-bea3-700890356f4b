# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies
node_modules/
*/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Backend specific
backend/uploads/
backend/logs/
backend/dist/
backend/build/

# Flutter specific
user-app/.dart_tool/
user-app/.flutter-plugins
user-app/.flutter-plugins-dependencies
user-app/.packages
user-app/.pub-cache/
user-app/.pub/
user-app/build/
user-app/ios/Pods/
user-app/ios/.symlinks/
user-app/ios/Flutter/Flutter.framework
user-app/ios/Flutter/Flutter.podspec
user-app/android/.gradle/
user-app/android/captures/
user-app/android/gradlew
user-app/android/gradlew.bat
user-app/android/local.properties
user-app/android/app/debug
user-app/android/app/profile
user-app/android/app/release

captain-app/.dart_tool/
captain-app/.flutter-plugins
captain-app/.flutter-plugins-dependencies
captain-app/.packages
captain-app/.pub-cache/
captain-app/.pub/
captain-app/build/
captain-app/ios/Pods/
captain-app/ios/.symlinks/
captain-app/ios/Flutter/Flutter.framework
captain-app/ios/Flutter/Flutter.podspec
captain-app/android/.gradle/
captain-app/android/captures/
captain-app/android/gradlew
captain-app/android/gradlew.bat
captain-app/android/local.properties
captain-app/android/app/debug
captain-app/android/app/profile
captain-app/android/app/release

# React specific
admin-dashboard/build/
admin-dashboard/.eslintcache

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Database
*.sqlite
*.db
