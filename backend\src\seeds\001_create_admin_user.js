const bcrypt = require('bcryptjs');

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.seed = async function(knex) {
  // Deletes ALL existing entries in users table with admin role
  await knex('users').where('user_type', 'admin').del();

  // Hash the default admin password
  const hashedPassword = await bcrypt.hash('password123', 12);

  // Insert default admin user
  await knex('users').insert([
    {
      id: knex.raw("(lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))"),
      phone: '01234567890',
      email: '<EMAIL>',
      name: 'مدير النظام',
      password: hashedPassword,
      user_type: 'admin',
      is_verified: true,
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    }
  ]);

  console.log('✅ Admin user created successfully');
  console.log('📧 Email: <EMAIL>');
  console.log('🔑 Password: password123');
  console.log('📱 Phone: 01234567890');
};
