import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'app_colors.dart';

class AppTextStyles {
  // Base text style
  static TextStyle get _baseTextStyle => GoogleFonts.cairo(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.normal,
      );

  // Display Styles
  static TextStyle get displayLarge => _baseTextStyle.copyWith(
        fontSize: 57,
        fontWeight: FontWeight.w400,
        letterSpacing: -0.25,
        height: 1.12,
      );

  static TextStyle get displayMedium => _baseTextStyle.copyWith(
        fontSize: 45,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        height: 1.16,
      );

  static TextStyle get displaySmall => _baseTextStyle.copyWith(
        fontSize: 36,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        height: 1.22,
      );

  // Headline Styles
  static TextStyle get headlineLarge => _baseTextStyle.copyWith(
        fontSize: 32,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        height: 1.25,
      );

  static TextStyle get headlineMedium => _baseTextStyle.copyWith(
        fontSize: 28,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        height: 1.29,
      );

  static TextStyle get headlineSmall => _baseTextStyle.copyWith(
        fontSize: 24,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        height: 1.33,
      );

  // Title Styles
  static TextStyle get titleLarge => _baseTextStyle.copyWith(
        fontSize: 22,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        height: 1.27,
      );

  static TextStyle get titleMedium => _baseTextStyle.copyWith(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.15,
        height: 1.50,
      );

  static TextStyle get titleSmall => _baseTextStyle.copyWith(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        height: 1.43,
      );

  // Body Styles
  static TextStyle get bodyLarge => _baseTextStyle.copyWith(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.5,
        height: 1.50,
      );

  static TextStyle get bodyMedium => _baseTextStyle.copyWith(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
        height: 1.43,
      );

  static TextStyle get bodySmall => _baseTextStyle.copyWith(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.4,
        height: 1.33,
      );

  // Label Styles
  static TextStyle get labelLarge => _baseTextStyle.copyWith(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        height: 1.43,
      );

  static TextStyle get labelMedium => _baseTextStyle.copyWith(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        height: 1.33,
      );

  static TextStyle get labelSmall => _baseTextStyle.copyWith(
        fontSize: 11,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        height: 1.45,
      );

  // Custom App Styles
  static TextStyle get appBarTitle => titleLarge.copyWith(
        fontWeight: FontWeight.w600,
        color: AppColors.textPrimary,
      );

  static TextStyle get buttonText => labelLarge.copyWith(
        fontWeight: FontWeight.w600,
        letterSpacing: 0.5,
      );

  static TextStyle get captionText => bodySmall.copyWith(
        color: AppColors.textSecondary,
      );

  static TextStyle get overlineText => labelSmall.copyWith(
        fontWeight: FontWeight.w500,
        letterSpacing: 1.5,
        color: AppColors.textSecondary,
      );

  // Price Styles
  static TextStyle get priceText => titleMedium.copyWith(
        fontWeight: FontWeight.w700,
        color: AppColors.primary,
      );

  static TextStyle get originalPriceText => bodyMedium.copyWith(
        decoration: TextDecoration.lineThrough,
        color: AppColors.textSecondary,
      );

  static TextStyle get discountText => labelMedium.copyWith(
        fontWeight: FontWeight.w600,
        color: AppColors.error,
      );

  // Status Styles
  static TextStyle get successText => bodyMedium.copyWith(
        color: AppColors.success,
        fontWeight: FontWeight.w500,
      );

  static TextStyle get errorText => bodyMedium.copyWith(
        color: AppColors.error,
        fontWeight: FontWeight.w500,
      );

  static TextStyle get warningText => bodyMedium.copyWith(
        color: AppColors.warning,
        fontWeight: FontWeight.w500,
      );

  static TextStyle get infoText => bodyMedium.copyWith(
        color: AppColors.info,
        fontWeight: FontWeight.w500,
      );

  // Rating Styles
  static TextStyle get ratingText => labelMedium.copyWith(
        fontWeight: FontWeight.w600,
        color: AppColors.warning,
      );

  // Link Styles
  static TextStyle get linkText => bodyMedium.copyWith(
        color: AppColors.primary,
        fontWeight: FontWeight.w500,
        decoration: TextDecoration.underline,
      );

  // Form Styles
  static TextStyle get inputText => bodyLarge.copyWith(
        color: AppColors.textPrimary,
      );

  static TextStyle get inputLabel => bodyMedium.copyWith(
        color: AppColors.textSecondary,
        fontWeight: FontWeight.w500,
      );

  static TextStyle get inputHint => bodyMedium.copyWith(
        color: AppColors.textHint,
      );

  static TextStyle get inputError => bodySmall.copyWith(
        color: AppColors.error,
        fontWeight: FontWeight.w500,
      );

  // Card Styles
  static TextStyle get cardTitle => titleMedium.copyWith(
        fontWeight: FontWeight.w600,
      );

  static TextStyle get cardSubtitle => bodyMedium.copyWith(
        color: AppColors.textSecondary,
      );

  // List Styles
  static TextStyle get listTitle => bodyLarge.copyWith(
        fontWeight: FontWeight.w500,
      );

  static TextStyle get listSubtitle => bodyMedium.copyWith(
        color: AppColors.textSecondary,
      );

  // Tab Styles
  static TextStyle get tabText => labelLarge.copyWith(
        fontWeight: FontWeight.w600,
      );

  // Dialog Styles
  static TextStyle get dialogTitle => headlineSmall.copyWith(
        fontWeight: FontWeight.w600,
      );

  static TextStyle get dialogContent => bodyLarge;

  // Snackbar Styles
  static TextStyle get snackbarText => bodyMedium.copyWith(
        color: AppColors.textOnPrimary,
      );

  // Badge Styles
  static TextStyle get badgeText => labelSmall.copyWith(
        fontWeight: FontWeight.w600,
        color: AppColors.textOnPrimary,
      );

  // Helper methods for text style modifications
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }

  static TextStyle withWeight(TextStyle style, FontWeight weight) {
    return style.copyWith(fontWeight: weight);
  }

  static TextStyle withSize(TextStyle style, double size) {
    return style.copyWith(fontSize: size);
  }

  static TextStyle withOpacity(TextStyle style, double opacity) {
    return style.copyWith(color: style.color?.withOpacity(opacity));
  }
}
