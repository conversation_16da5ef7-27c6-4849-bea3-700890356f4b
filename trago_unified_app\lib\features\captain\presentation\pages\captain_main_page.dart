import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';

class CaptainMainPage extends ConsumerStatefulWidget {
  final int initialIndex;
  
  const CaptainMainPage({
    super.key,
    this.initialIndex = 0,
  });

  @override
  ConsumerState<CaptainMainPage> createState() => _CaptainMainPageState();
}

class _CaptainMainPageState extends ConsumerState<CaptainMainPage> {
  late int _currentIndex;
  late PageController _pageController;

  final List<CaptainBottomNavItem> _navItems = [
    CaptainBottomNavItem(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'الرئيسية',
    ),
    CaptainBottomNavItem(
      icon: Icons.delivery_dining_outlined,
      activeIcon: Icons.delivery_dining,
      label: 'الطلبات',
    ),
    Captain<PERSON>ottomNavItem(
      icon: Icons.monetization_on_outlined,
      activeIcon: Icons.monetization_on,
      label: 'الأرباح',
    ),
    CaptainBottomNavItem(
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      label: 'الملف الشخصي',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        children: [
          _buildHomePage(),
          _buildOrdersPage(),
          _buildEarningsPage(),
          _buildProfilePage(),
        ],
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
          _pageController.animateToPage(
            index,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        },
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppColors.surface,
        selectedItemColor: AppColors.captainPrimary,
        unselectedItemColor: AppColors.textSecondary,
        selectedLabelStyle: AppTextStyles.labelSmall.copyWith(
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: AppTextStyles.labelSmall,
        items: _navItems.map((item) {
          final isSelected = _navItems.indexOf(item) == _currentIndex;
          return BottomNavigationBarItem(
            icon: Icon(isSelected ? item.activeIcon : item.icon),
            label: item.label,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildHomePage() {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'مرحباً كابتن',
          style: AppTextStyles.appBarTitle,
        ),
        backgroundColor: AppColors.captainPrimary,
        foregroundColor: AppColors.textOnPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // Handle notifications
            },
          ),
          // Online/Offline Toggle
          Switch(
            value: true, // This should come from state
            onChanged: (value) {
              // Handle online/offline toggle
            },
            activeColor: AppColors.success,
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.delivery_dining,
              size: 100,
              color: AppColors.captainPrimary,
            ),
            SizedBox(height: 20),
            Text(
              'صفحة الكابتن الرئيسية',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10),
            Text(
              'ابدأ استقبال الطلبات واربح المال',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrdersPage() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الطلبات'),
        backgroundColor: AppColors.captainPrimary,
        foregroundColor: AppColors.textOnPrimary,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.delivery_dining,
              size: 100,
              color: AppColors.captainPrimary,
            ),
            SizedBox(height: 20),
            Text(
              'طلبات التوصيل',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10),
            Text(
              'إدارة طلبات التوصيل الخاصة بك',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEarningsPage() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الأرباح'),
        backgroundColor: AppColors.captainPrimary,
        foregroundColor: AppColors.textOnPrimary,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.monetization_on,
              size: 100,
              color: AppColors.captainPrimary,
            ),
            SizedBox(height: 20),
            Text(
              'الأرباح',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10),
            Text(
              'تتبع أرباحك اليومية والشهرية',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfilePage() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الملف الشخصي'),
        backgroundColor: AppColors.captainPrimary,
        foregroundColor: AppColors.textOnPrimary,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.person,
              size: 100,
              color: AppColors.captainPrimary,
            ),
            SizedBox(height: 20),
            Text(
              'الملف الشخصي',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10),
            Text(
              'إدارة حسابك ومعلومات المركبة',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CaptainBottomNavItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;

  CaptainBottomNavItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
  });
}
