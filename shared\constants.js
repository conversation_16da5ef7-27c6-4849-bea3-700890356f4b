// Trago - Shared Constants

// App Information
export const APP_INFO = {
  NAME: 'Trago',
  NAME_ARABIC: 'تراجو',
  VERSION: '1.0.0',
  DESCRIPTION: 'تطبيق توصيل الطلبات',
  DESCRIPTION_EN: 'Food & Store Delivery App'
};

// User Types
export const USER_TYPES = {
  CUSTOMER: 'customer',
  CAPTAIN: 'captain',
  STORE_OWNER: 'store_owner',
  ADMIN: 'admin'
};

// Order Status
export const ORDER_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PREPARING: 'preparing',
  READY: 'ready',
  PICKED_UP: 'picked_up',
  ON_THE_WAY: 'on_the_way',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled'
};

// Order Status Labels (Arabic)
export const ORDER_STATUS_LABELS_AR = {
  [ORDER_STATUS.PENDING]: 'في انتظار التأكيد',
  [ORDER_STATUS.CONFIRMED]: 'تم التأكيد',
  [ORDER_STATUS.PREPARING]: 'جاري التحضير',
  [ORDER_STATUS.READY]: 'جاهز للاستلام',
  [ORDER_STATUS.PICKED_UP]: 'تم الاستلام',
  [ORDER_STATUS.ON_THE_WAY]: 'في الطريق',
  [ORDER_STATUS.DELIVERED]: 'تم التوصيل',
  [ORDER_STATUS.CANCELLED]: 'تم الإلغاء'
};

// Order Status Labels (English)
export const ORDER_STATUS_LABELS_EN = {
  [ORDER_STATUS.PENDING]: 'Pending',
  [ORDER_STATUS.CONFIRMED]: 'Confirmed',
  [ORDER_STATUS.PREPARING]: 'Preparing',
  [ORDER_STATUS.READY]: 'Ready',
  [ORDER_STATUS.PICKED_UP]: 'Picked Up',
  [ORDER_STATUS.ON_THE_WAY]: 'On The Way',
  [ORDER_STATUS.DELIVERED]: 'Delivered',
  [ORDER_STATUS.CANCELLED]: 'Cancelled'
};

// Payment Methods
export const PAYMENT_METHODS = {
  CASH: 'cash',
  VODAFONE_CASH: 'vodafone_cash',
  FAWRY: 'fawry',
  ORANGE_CASH: 'orange_cash',
  ETISALAT_CASH: 'etisalat_cash'
};

// Payment Method Labels (Arabic)
export const PAYMENT_METHOD_LABELS_AR = {
  [PAYMENT_METHODS.CASH]: 'الدفع عند الاستلام',
  [PAYMENT_METHODS.VODAFONE_CASH]: 'فودافون كاش',
  [PAYMENT_METHODS.FAWRY]: 'فوري',
  [PAYMENT_METHODS.ORANGE_CASH]: 'أورانج كاش',
  [PAYMENT_METHODS.ETISALAT_CASH]: 'اتصالات كاش'
};

// Payment Method Labels (English)
export const PAYMENT_METHOD_LABELS_EN = {
  [PAYMENT_METHODS.CASH]: 'Cash on Delivery',
  [PAYMENT_METHODS.VODAFONE_CASH]: 'Vodafone Cash',
  [PAYMENT_METHODS.FAWRY]: 'Fawry',
  [PAYMENT_METHODS.ORANGE_CASH]: 'Orange Cash',
  [PAYMENT_METHODS.ETISALAT_CASH]: 'Etisalat Cash'
};

// Store Categories
export const STORE_CATEGORIES = {
  RESTAURANT: 'restaurant',
  CAFE: 'cafe',
  GROCERY: 'grocery',
  PHARMACY: 'pharmacy',
  ELECTRONICS: 'electronics',
  CLOTHING: 'clothing',
  BOOKS: 'books',
  OTHER: 'other'
};

// Store Category Labels (Arabic)
export const STORE_CATEGORY_LABELS_AR = {
  [STORE_CATEGORIES.RESTAURANT]: 'مطعم',
  [STORE_CATEGORIES.CAFE]: 'مقهى',
  [STORE_CATEGORIES.GROCERY]: 'بقالة',
  [STORE_CATEGORIES.PHARMACY]: 'صيدلية',
  [STORE_CATEGORIES.ELECTRONICS]: 'إلكترونيات',
  [STORE_CATEGORIES.CLOTHING]: 'ملابس',
  [STORE_CATEGORIES.BOOKS]: 'كتب',
  [STORE_CATEGORIES.OTHER]: 'أخرى'
};

// Store Category Labels (English)
export const STORE_CATEGORY_LABELS_EN = {
  [STORE_CATEGORIES.RESTAURANT]: 'Restaurant',
  [STORE_CATEGORIES.CAFE]: 'Cafe',
  [STORE_CATEGORIES.GROCERY]: 'Grocery',
  [STORE_CATEGORIES.PHARMACY]: 'Pharmacy',
  [STORE_CATEGORIES.ELECTRONICS]: 'Electronics',
  [STORE_CATEGORIES.CLOTHING]: 'Clothing',
  [STORE_CATEGORIES.BOOKS]: 'Books',
  [STORE_CATEGORIES.OTHER]: 'Other'
};

// Captain Status
export const CAPTAIN_STATUS = {
  OFFLINE: 'offline',
  ONLINE: 'online',
  BUSY: 'busy'
};

// Captain Status Labels (Arabic)
export const CAPTAIN_STATUS_LABELS_AR = {
  [CAPTAIN_STATUS.OFFLINE]: 'غير متصل',
  [CAPTAIN_STATUS.ONLINE]: 'متاح',
  [CAPTAIN_STATUS.BUSY]: 'مشغول'
};

// Captain Status Labels (English)
export const CAPTAIN_STATUS_LABELS_EN = {
  [CAPTAIN_STATUS.OFFLINE]: 'Offline',
  [CAPTAIN_STATUS.ONLINE]: 'Online',
  [CAPTAIN_STATUS.BUSY]: 'Busy'
};

// Vehicle Types
export const VEHICLE_TYPES = {
  MOTORCYCLE: 'motorcycle',
  CAR: 'car',
  BICYCLE: 'bicycle'
};

// Vehicle Type Labels (Arabic)
export const VEHICLE_TYPE_LABELS_AR = {
  [VEHICLE_TYPES.MOTORCYCLE]: 'دراجة نارية',
  [VEHICLE_TYPES.CAR]: 'سيارة',
  [VEHICLE_TYPES.BICYCLE]: 'دراجة هوائية'
};

// Vehicle Type Labels (English)
export const VEHICLE_TYPE_LABELS_EN = {
  [VEHICLE_TYPES.MOTORCYCLE]: 'Motorcycle',
  [VEHICLE_TYPES.CAR]: 'Car',
  [VEHICLE_TYPES.BICYCLE]: 'Bicycle'
};

// API Endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    VERIFY_OTP: '/api/auth/verify-otp',
    REFRESH: '/api/auth/refresh',
    LOGOUT: '/api/auth/logout'
  },
  USER: {
    PROFILE: '/api/user/profile',
    ADDRESSES: '/api/user/addresses',
    ORDERS: '/api/user/orders'
  },
  STORES: {
    LIST: '/api/stores',
    DETAILS: '/api/stores/:id',
    MENU: '/api/stores/:id/menu',
    SEARCH: '/api/stores/search'
  },
  ORDERS: {
    CREATE: '/api/orders',
    LIST: '/api/orders',
    DETAILS: '/api/orders/:id',
    UPDATE_STATUS: '/api/orders/:id/status',
    TRACK: '/api/orders/:id/track'
  },
  CAPTAIN: {
    REGISTER: '/api/captain/register',
    STATUS: '/api/captain/status',
    ORDERS: '/api/captain/orders',
    ACCEPT_ORDER: '/api/captain/accept-order',
    UPDATE_LOCATION: '/api/captain/location'
  }
};

// Validation Rules
export const VALIDATION = {
  PHONE: {
    MIN_LENGTH: 11,
    MAX_LENGTH: 11,
    PATTERN: /^01[0-2,5]{1}[0-9]{8}$/
  },
  PASSWORD: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 50,
    PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
  },
  NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 50
  },
  OTP: {
    LENGTH: 6,
    PATTERN: /^\d{6}$/
  }
};

// Default Values
export const DEFAULTS = {
  DELIVERY_FEE: 15,
  MAX_DELIVERY_DISTANCE: 20, // km
  DELIVERY_TIME_ESTIMATE: 30, // minutes
  PAGINATION_LIMIT: 20,
  SEARCH_RADIUS: 10, // km
  MAP_ZOOM_LEVEL: 15
};

// Socket Events
export const SOCKET_EVENTS = {
  ORDER_CREATED: 'order_created',
  ORDER_UPDATED: 'order_updated',
  CAPTAIN_LOCATION_UPDATED: 'captain_location_updated',
  NOTIFICATION: 'notification'
};

// Notification Types
export const NOTIFICATION_TYPES = {
  ORDER_CONFIRMED: 'order_confirmed',
  ORDER_PREPARING: 'order_preparing',
  ORDER_READY: 'order_ready',
  ORDER_PICKED_UP: 'order_picked_up',
  ORDER_ON_THE_WAY: 'order_on_the_way',
  ORDER_DELIVERED: 'order_delivered',
  ORDER_CANCELLED: 'order_cancelled',
  NEW_ORDER: 'new_order',
  CAPTAIN_ASSIGNED: 'captain_assigned'
};
