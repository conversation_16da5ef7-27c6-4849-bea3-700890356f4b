import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/storage_service.dart';
import '../config/app_config.dart';

class LocaleNotifier extends StateNotifier<Locale> {
  LocaleNotifier() : super(AppConfig.defaultLocale) {
    _loadLocale();
  }

  void _loadLocale() {
    final savedLanguage = StorageService.getLanguage();
    state = Locale(savedLanguage);
  }

  Future<void> setLocale(Locale locale) async {
    if (AppConfig.supportedLocales.contains(locale)) {
      state = locale;
      await StorageService.setLanguage(locale.languageCode);
    }
  }

  Future<void> setLanguage(String languageCode) async {
    final locale = Locale(languageCode);
    await setLocale(locale);
  }

  Future<void> toggleLanguage() async {
    final newLanguage = state.languageCode == 'ar' ? 'en' : 'ar';
    await setLanguage(newLanguage);
  }

  bool get isArabic => state.languageCode == 'ar';
  bool get isEnglish => state.languageCode == 'en';
  String get currentLanguage => state.languageCode;
  String get currentLanguageName => isArabic ? 'العربية' : 'English';
  String get otherLanguageName => isArabic ? 'English' : 'العربية';
}

final localeProvider = StateNotifierProvider<LocaleNotifier, Locale>((ref) {
  return LocaleNotifier();
});

// Helper providers
final isArabicProvider = Provider<bool>((ref) {
  final locale = ref.watch(localeProvider);
  return locale.languageCode == 'ar';
});

final isEnglishProvider = Provider<bool>((ref) {
  final locale = ref.watch(localeProvider);
  return locale.languageCode == 'en';
});

final currentLanguageProvider = Provider<String>((ref) {
  final locale = ref.watch(localeProvider);
  return locale.languageCode;
});

final textDirectionProvider = Provider<TextDirection>((ref) {
  final isArabic = ref.watch(isArabicProvider);
  return isArabic ? TextDirection.rtl : TextDirection.ltr;
});
