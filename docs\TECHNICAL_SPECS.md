# المواصفات التقنية - Trago

## نظرة عامة على البنية التقنية

### Backend API (Node.js + Express)
- **Framework**: Express.js
- **Database**: PostgreSQL (الرئيسية) + Redis (التخزين المؤقت)
- **Authentication**: JWT + Refresh Tokens
- **File Upload**: Cloudinary / AWS S3
- **Real-time**: Socket.io
- **Payment Integration**: Multiple Egyptian payment gateways
- **Maps**: Google Maps API
- **Notifications**: Firebase Cloud Messaging

### Mobile Apps (Flutter)
- **Framework**: Flutter 3.0+
- **State Management**: Provider / Riverpod
- **Local Storage**: Hive / SharedPreferences
- **HTTP Client**: Dio
- **Maps**: Google Maps Flutter
- **Notifications**: Firebase Messaging
- **Localization**: flutter_localizations

### Admin Dashboard (React.js)
- **Framework**: React 18
- **UI Library**: Material-UI / Ant Design
- **State Management**: Redux Toolkit
- **Charts**: Chart.js / Recharts
- **Maps**: Google Maps React
- **HTTP Client**: Axios

## قاعدة البيانات

### الجداول الرئيسية

#### Users (المستخدمين)
```sql
- id (UUID, Primary Key)
- phone (VARCHAR, Unique)
- email (VARCHAR, Unique)
- name (VARCHAR)
- avatar (VARCHAR)
- is_verified (BOOLEAN)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### Captains (الكباتن)
```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key)
- license_number (VARCHAR)
- vehicle_type (ENUM)
- vehicle_plate (VARCHAR)
- is_approved (BOOLEAN)
- is_online (BOOLEAN)
- current_location (POINT)
- rating (DECIMAL)
- total_orders (INTEGER)
```

#### Stores (المتاجر)
```sql
- id (UUID, Primary Key)
- name (VARCHAR)
- description (TEXT)
- category (VARCHAR)
- address (TEXT)
- location (POINT)
- phone (VARCHAR)
- is_active (BOOLEAN)
- rating (DECIMAL)
- delivery_fee (DECIMAL)
- min_order (DECIMAL)
```

#### Orders (الطلبات)
```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key)
- store_id (UUID, Foreign Key)
- captain_id (UUID, Foreign Key, Nullable)
- status (ENUM)
- total_amount (DECIMAL)
- delivery_fee (DECIMAL)
- payment_method (ENUM)
- delivery_address (TEXT)
- delivery_location (POINT)
- estimated_delivery (TIMESTAMP)
- created_at (TIMESTAMP)
```

## APIs الرئيسية

### Authentication APIs
- `POST /api/auth/register` - تسجيل مستخدم جديد
- `POST /api/auth/login` - تسجيل الدخول
- `POST /api/auth/verify-otp` - التحقق من OTP
- `POST /api/auth/refresh` - تجديد التوكن
- `POST /api/auth/logout` - تسجيل الخروج

### User APIs
- `GET /api/user/profile` - الحصول على الملف الشخصي
- `PUT /api/user/profile` - تحديث الملف الشخصي
- `GET /api/user/addresses` - الحصول على العناوين
- `POST /api/user/addresses` - إضافة عنوان جديد

### Store APIs
- `GET /api/stores` - الحصول على قائمة المتاجر
- `GET /api/stores/:id` - تفاصيل متجر
- `GET /api/stores/:id/menu` - قائمة المنتجات

### Order APIs
- `POST /api/orders` - إنشاء طلب جديد
- `GET /api/orders` - الحصول على طلبات المستخدم
- `GET /api/orders/:id` - تفاصيل الطلب
- `PUT /api/orders/:id/status` - تحديث حالة الطلب

### Captain APIs
- `POST /api/captain/register` - تسجيل كابتن جديد
- `PUT /api/captain/status` - تحديث الحالة (متاح/غير متاح)
- `GET /api/captain/orders` - الحصول على الطلبات المتاحة
- `POST /api/captain/accept-order` - قبول طلب

## نظام الدفع

### الطرق المدعومة
1. **فودافون كاش (Vodafone Cash)**
2. **فوري (Fawry)**
3. **أورانج كاش (Orange Cash)**
4. **اتصالات كاش (Etisalat Cash)**
5. **الدفع عند الاستلام (COD)**

### تدفق الدفع
1. المستخدم يختار طريقة الدفع
2. إنشاء معاملة دفع
3. إعادة توجيه للبوابة المناسبة
4. التحقق من حالة الدفع
5. تأكيد الطلب

## نظام التتبع

### تتبع الطلب
- حالة الطلب في الوقت الفعلي
- موقع الكابتن على الخريطة
- الوقت المتوقع للوصول
- إشعارات تلقائية للتحديثات

### حالات الطلب
1. `PENDING` - في انتظار التأكيد
2. `CONFIRMED` - تم تأكيد الطلب
3. `PREPARING` - جاري التحضير
4. `READY` - جاهز للاستلام
5. `PICKED_UP` - تم الاستلام من المتجر
6. `ON_THE_WAY` - في الطريق
7. `DELIVERED` - تم التوصيل
8. `CANCELLED` - تم الإلغاء

## الأمان

### تشفير البيانات
- HTTPS لجميع الاتصالات
- تشفير كلمات المرور باستخدام bcrypt
- تشفير البيانات الحساسة في قاعدة البيانات

### المصادقة والتخويل
- JWT Tokens مع انتهاء صلاحية
- Refresh Tokens للجلسات الطويلة
- Rate Limiting لمنع الهجمات
- Input validation وsanitization

## الأداء

### التحسينات
- Database indexing
- Redis caching
- Image optimization
- API response compression
- Lazy loading في التطبيقات

### المراقبة
- Application logs
- Performance monitoring
- Error tracking
- Database query optimization
