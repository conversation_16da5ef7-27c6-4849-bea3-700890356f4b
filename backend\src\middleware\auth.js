const jwt = require('jsonwebtoken');
const db = require('../config/database');
const redis = require('../config/redis');
const logger = require('../utils/logger');

const auth = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.',
        message_ar: 'تم رفض الوصول. لم يتم توفير رمز المصادقة.'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Check if token is blacklisted
    const isBlacklisted = await redis.get(`blacklist_${token}`);
    if (isBlacklisted) {
      return res.status(401).json({
        success: false,
        message: 'Token has been invalidated.',
        message_ar: 'تم إبطال رمز المصادقة.'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Check if user still exists
    const user = await db('users')
      .select('id', 'phone', 'email', 'name', 'user_type', 'is_verified', 'is_active')
      .where('id', decoded.userId)
      .first();

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'User not found.',
        message_ar: 'المستخدم غير موجود.'
      });
    }

    // Check if user is active
    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account has been deactivated.',
        message_ar: 'تم إلغاء تفعيل الحساب.'
      });
    }

    // Add user info to request
    req.user = {
      id: user.id,
      phone: user.phone,
      email: user.email,
      name: user.name,
      userType: user.user_type,
      isVerified: user.is_verified
    };

    // Cache user info in Redis for 1 hour
    await redis.set(`user_${user.id}`, JSON.stringify(req.user), 3600);

    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token.',
        message_ar: 'رمز مصادقة غير صحيح.'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token has expired.',
        message_ar: 'انتهت صلاحية رمز المصادقة.'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Authentication error.',
      message_ar: 'خطأ في المصادقة.'
    });
  }
};

// Middleware to check if user is verified
const requireVerified = (req, res, next) => {
  if (!req.user.isVerified) {
    return res.status(403).json({
      success: false,
      message: 'Account verification required.',
      message_ar: 'يجب التحقق من الحساب أولاً.'
    });
  }
  next();
};

// Middleware to check user type
const requireUserType = (userTypes) => {
  return (req, res, next) => {
    if (!userTypes.includes(req.user.userType)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions.',
        message_ar: 'صلاحيات غير كافية.'
      });
    }
    next();
  };
};

// Middleware for admin only
const requireAdmin = requireUserType(['admin']);

// Middleware for captain only
const requireCaptain = requireUserType(['captain']);

// Middleware for customer only
const requireCustomer = requireUserType(['customer']);

// Middleware for store owner only
const requireStoreOwner = requireUserType(['store_owner']);

// Optional auth middleware (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const token = authHeader.substring(7);
    
    // Check if token is blacklisted
    const isBlacklisted = await redis.get(`blacklist_${token}`);
    if (isBlacklisted) {
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    const user = await db('users')
      .select('id', 'phone', 'email', 'name', 'user_type', 'is_verified', 'is_active')
      .where('id', decoded.userId)
      .first();

    if (user && user.is_active) {
      req.user = {
        id: user.id,
        phone: user.phone,
        email: user.email,
        name: user.name,
        userType: user.user_type,
        isVerified: user.is_verified
      };
    }

    next();
  } catch (error) {
    // Silently continue without user info
    next();
  }
};

module.exports = {
  auth,
  requireVerified,
  requireUserType,
  requireAdmin,
  requireCaptain,
  requireCustomer,
  requireStoreOwner,
  optionalAuth
};
