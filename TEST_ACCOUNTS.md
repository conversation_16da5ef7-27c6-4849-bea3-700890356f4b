# حسابات التجربة - Test Accounts

تم إنشاء حسابات تجريبية لاختبار التطبيق بالكامل. يمكنك استخدام هذه الحسابات لتجربة جميع ميزات التطبيق.

## 🎯 الحسابات المتاحة

### 👤 حساب العميل (User App)
استخدم هذا الحساب لتجربة تطبيق العملاء:

- **البريد الإلكتروني:** `<EMAIL>`
- **رقم الهاتف:** `***********`
- **كلمة المرور:** `Test123456`
- **نوع المستخدم:** عميل (Customer)
- **الحالة:** مفعل ومتحقق منه

### 🏍️ حساب الكابتن (Captain App)
استخدم هذا الحساب لتجربة تطبيق الكابتنز:

- **البريد الإلكتروني:** `<EMAIL>`
- **رقم الهاتف:** `***********`
- **كلمة المرور:** `Test123456`
- **نوع المستخدم:** كابتن (Captain)
- **الحالة:** مفعل ومتحقق منه ومعتمد
- **رقم الرخصة:** `TEST123456`
- **نوع المركبة:** دراجة نارية
- **رقم اللوحة:** `ABC 1234`
- **موديل المركبة:** Honda CBR
- **لون المركبة:** أحمر
- **التقييم:** 4.5/5

### 👨‍💼 حساب المدير (Admin Dashboard)
للوصول إلى لوحة التحكم الإدارية:

- **البريد الإلكتروني:** `<EMAIL>`
- **رقم الهاتف:** `01234567890`
- **كلمة المرور:** `password123`
- **نوع المستخدم:** مدير (Admin)

## 🚀 كيفية الاستخدام

### 1. تشغيل الخادم الخلفي (Backend)
```bash
cd backend
npm install
npm run migrate  # تشغيل migrations
npm run seed     # إنشاء البيانات التجريبية
npm run dev      # تشغيل الخادم
```

### 2. تجربة تطبيق العملاء (User App)
```bash
cd user-app
flutter pub get
flutter run
```
- استخدم بيانات حساب العميل للدخول
- جرب طلب منتجات وإنشاء طلبات

### 3. تجربة تطبيق الكابتنز (Captain App)
```bash
cd captain-app
flutter pub get
flutter run
```
- استخدم بيانات حساب الكابتن للدخول
- جرب قبول الطلبات وتتبع التوصيلات

### 4. تجربة لوحة التحكم الإدارية
```bash
cd admin-dashboard
npm install
npm start
```
- استخدم بيانات حساب المدير للدخول
- جرب إدارة المستخدمين والطلبات

## 📱 نصائح للتجربة

### للعملاء:
- جرب إنشاء طلب جديد
- اختبر إضافة عناوين متعددة
- جرب تتبع الطلب في الوقت الفعلي
- اختبر تقييم الكابتن بعد التوصيل

### للكابتنز:
- جرب تغيير الحالة (متاح/مشغول/غير متاح)
- اختبر قبول ورفض الطلبات
- جرب تحديث موقعك الحالي
- اختبر إكمال التوصيل

### للمدراء:
- جرب إدارة المستخدمين
- اختبر الموافقة على الكابتنز الجدد
- جرب مراقبة الطلبات والإحصائيات
- اختبر إدارة المتاجر والمنتجات

## 🔧 إعادة إنشاء البيانات

إذا كنت تريد إعادة إنشاء البيانات التجريبية:

```bash
cd backend
npm run seed
```

## 📞 الدعم

إذا واجهت أي مشاكل في استخدام هذه الحسابات، تأكد من:

1. تشغيل الخادم الخلفي أولاً
2. تشغيل migrations و seeds
3. التأكد من صحة بيانات الدخول
4. فحص logs الخادم للأخطاء

---

**ملاحظة:** هذه حسابات تجريبية فقط ولا يجب استخدامها في بيئة الإنتاج.
