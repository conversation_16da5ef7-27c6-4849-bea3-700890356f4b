import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/models/user_model.dart';
import '../../../../core/services/storage_service.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../data/repositories/auth_repository_impl.dart';

class AuthState {
  final bool isLoading;
  final bool isAuthenticated;
  final UserModel? user;
  final String? error;

  const AuthState({
    this.isLoading = false,
    this.isAuthenticated = false,
    this.user,
    this.error,
  });

  AuthState copyWith({
    bool? isLoading,
    bool? isAuthenticated,
    UserModel? user,
    String? error,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      user: user ?? this.user,
      error: error,
    );
  }
}

class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepositoryInterface _authRepository;

  AuthNotifier(this._authRepository) : super(const AuthState()) {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    try {
      final isLoggedIn = await StorageService.isUserLoggedIn();
      if (isLoggedIn) {
        final user = await StorageService.getUserData();
        if (user != null) {
          state = state.copyWith(
            isAuthenticated: true,
            user: user,
          );
        }
      }
    } catch (e) {
      debugPrint('Error checking auth status: $e');
    }
  }

  Future<bool> login({
    required String phone,
    required String password,
    required String userType,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _authRepository.login(
        phone: phone,
        password: password,
        userType: userType,
      );

      if (result.isSuccess && result.data != null) {
        await StorageService.saveUserData(result.data!);

        state = state.copyWith(
          isLoading: false,
          isAuthenticated: true,
          user: result.data,
          error: null,
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'حدث خطأ أثناء تسجيل الدخول',
      );
      return false;
    }
  }

  Future<bool> register({
    required String name,
    required String phone,
    required String email,
    required String password,
    required String userType,
    Map<String, dynamic>? additionalData,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _authRepository.register(
        name: name,
        phone: phone,
        email: email,
        password: password,
        userType: userType,
        additionalData: additionalData,
      );

      if (result.isSuccess) {
        state = state.copyWith(
          isLoading: false,
          error: null,
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'حدث خطأ أثناء إنشاء الحساب',
      );
      return false;
    }
  }

  Future<bool> verifyOTP({
    required String phone,
    required String otp,
    required String userType,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _authRepository.verifyOTP(
        phone: phone,
        otp: otp,
        userType: userType,
      );

      if (result.isSuccess && result.data != null) {
        await StorageService.saveUserData(result.data!);

        state = state.copyWith(
          isLoading: false,
          isAuthenticated: true,
          user: result.data,
          error: null,
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'حدث خطأ أثناء التحقق من الرمز',
      );
      return false;
    }
  }

  Future<bool> resendOTP({
    required String phone,
    required String userType,
  }) async {
    try {
      final result = await _authRepository.resendOTP(
        phone: phone,
        userType: userType,
      );

      if (!result.isSuccess) {
        state = state.copyWith(error: result.message);
      }

      return result.isSuccess;
    } catch (e) {
      state = state.copyWith(error: 'حدث خطأ أثناء إعادة إرسال الرمز');
      return false;
    }
  }

  Future<bool> forgotPassword({
    required String phone,
    required String userType,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _authRepository.forgotPassword(
        phone: phone,
        userType: userType,
      );

      state = state.copyWith(
        isLoading: false,
        error: result.isSuccess ? null : result.message,
      );

      return result.isSuccess;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'حدث خطأ أثناء إرسال رمز استعادة كلمة المرور',
      );
      return false;
    }
  }

  Future<bool> resetPassword({
    required String phone,
    required String otp,
    required String newPassword,
    required String userType,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _authRepository.resetPassword(
        phone: phone,
        otp: otp,
        newPassword: newPassword,
        userType: userType,
      );

      state = state.copyWith(
        isLoading: false,
        error: result.isSuccess ? null : result.message,
      );

      return result.isSuccess;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'حدث خطأ أثناء إعادة تعيين كلمة المرور',
      );
      return false;
    }
  }

  Future<void> logout() async {
    try {
      await _authRepository.logout();
      await StorageService.clearUserData();

      state = const AuthState(
        isAuthenticated: false,
        user: null,
      );
    } catch (e) {
      debugPrint('Error during logout: $e');
    }
  }

  Future<void> updateProfile({
    required String name,
    String? email,
    String? avatar,
  }) async {
    if (state.user == null) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _authRepository.updateProfile(
        name: name,
        email: email,
        avatar: avatar,
      );

      if (result.isSuccess && result.data != null) {
        await StorageService.saveUserData(result.data!);

        state = state.copyWith(
          isLoading: false,
          user: result.data,
          error: null,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'حدث خطأ أثناء تحديث الملف الشخصي',
      );
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

final authRepositoryProvider = Provider<AuthRepositoryInterface>((ref) {
  return AuthRepository();
});

final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authRepository = ref.watch(authRepositoryProvider);
  return AuthNotifier(authRepository);
});

// Helper providers
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

final currentUserProvider = Provider<UserModel?>((ref) {
  return ref.watch(authProvider).user;
});

final authErrorProvider = Provider<String?>((ref) {
  return ref.watch(authProvider).error;
});

final isLoadingProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isLoading;
});
