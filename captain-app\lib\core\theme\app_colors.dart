import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF2E7D32); // Green
  static const Color primaryLight = Color(0xFF60AD5E);
  static const Color primaryDark = Color(0xFF005005);
  
  // Secondary Colors
  static const Color secondary = Color(0xFFFF6B35); // Orange
  static const Color secondaryLight = Color(0xFFFF9B6B);
  static const Color secondaryDark = Color(0xFFC73E00);
  
  // Background Colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF8F8F8);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textOnSecondary = Color(0xFFFFFFFF);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Order Status Colors
  static const Color orderPending = Color(0xFFFF9800); // Orange
  static const Color orderConfirmed = Color(0xFF2196F3); // Blue
  static const Color orderPreparing = Color(0xFF9C27B0); // Purple
  static const Color orderReady = Color(0xFF4CAF50); // Green
  static const Color orderPickedUp = Color(0xFF00BCD4); // Cyan
  static const Color orderOnTheWay = Color(0xFF3F51B5); // Indigo
  static const Color orderDelivered = Color(0xFF4CAF50); // Green
  static const Color orderCancelled = Color(0xFFF44336); // Red
  
  // Captain Status Colors
  static const Color captainOffline = Color(0xFF9E9E9E); // Grey
  static const Color captainOnline = Color(0xFF4CAF50); // Green
  static const Color captainBusy = Color(0xFFFF9800); // Orange
  
  // Vehicle Type Colors
  static const Color vehicleBicycle = Color(0xFF4CAF50);
  static const Color vehicleMotorcycle = Color(0xFF2196F3);
  static const Color vehicleCar = Color(0xFF9C27B0);
  static const Color vehicleWalking = Color(0xFFFF9800);
  
  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderLight = Color(0xFFF0F0F0);
  static const Color borderDark = Color(0xFFBDBDBD);
  
  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);
  static const Color shadowDark = Color(0x33000000);
  
  // Overlay Colors
  static const Color overlay = Color(0x80000000);
  static const Color overlayLight = Color(0x40000000);
  
  // Map Colors
  static const Color mapRoute = Color(0xFF2196F3);
  static const Color mapMarkerCaptain = Color(0xFF4CAF50);
  static const Color mapMarkerCustomer = Color(0xFFFF6B35);
  static const Color mapMarkerStore = Color(0xFF9C27B0);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient successGradient = LinearGradient(
    colors: [success, Color(0xFF2E7D32)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient warningGradient = LinearGradient(
    colors: [warning, Color(0xFFE65100)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient errorGradient = LinearGradient(
    colors: [error, Color(0xFFD32F2F)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Helper Methods
  static Color getOrderStatusColor(String status) {
    switch (status) {
      case 'pending':
        return orderPending;
      case 'confirmed':
        return orderConfirmed;
      case 'preparing':
        return orderPreparing;
      case 'ready':
        return orderReady;
      case 'picked_up':
        return orderPickedUp;
      case 'on_the_way':
        return orderOnTheWay;
      case 'delivered':
        return orderDelivered;
      case 'cancelled':
        return orderCancelled;
      default:
        return textSecondary;
    }
  }
  
  static Color getCaptainStatusColor(String status) {
    switch (status) {
      case 'offline':
        return captainOffline;
      case 'online':
        return captainOnline;
      case 'busy':
        return captainBusy;
      default:
        return textSecondary;
    }
  }
  
  static Color getVehicleTypeColor(String vehicleType) {
    switch (vehicleType) {
      case 'bicycle':
        return vehicleBicycle;
      case 'motorcycle':
        return vehicleMotorcycle;
      case 'car':
        return vehicleCar;
      case 'walking':
        return vehicleWalking;
      default:
        return textSecondary;
    }
  }
}
