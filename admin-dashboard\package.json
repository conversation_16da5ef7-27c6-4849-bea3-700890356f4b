{"name": "trago-admin-dashboard", "version": "1.0.0", "description": "Trago Admin Dashboard - React Application", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.1", "axios": "^1.6.2", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "recharts": "^2.8.0", "moment": "^2.29.4", "socket.io-client": "^4.7.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "npx react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3002"}