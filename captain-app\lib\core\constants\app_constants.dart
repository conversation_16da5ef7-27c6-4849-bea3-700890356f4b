import 'package:flutter/material.dart';

class AppConstants {
  // App Information
  static const String appName = 'Trago Captain';
  static const String appNameArabic = 'تراجو كابتن';
  static const String appVersion = '1.0.0';

  // User Types
  static const String userTypeCustomer = 'customer';
  static const String userTypeCaptain = 'captain';
  static const String userTypeStoreOwner = 'store_owner';
  static const String userTypeAdmin = 'admin';

  // Order Status
  static const String orderStatusPending = 'pending';
  static const String orderStatusConfirmed = 'confirmed';
  static const String orderStatusPreparing = 'preparing';
  static const String orderStatusReady = 'ready';
  static const String orderStatusPickedUp = 'picked_up';
  static const String orderStatusOnTheWay = 'on_the_way';
  static const String orderStatusDelivered = 'delivered';
  static const String orderStatusCancelled = 'cancelled';

  // Captain Status
  static const String captainStatusOffline = 'offline';
  static const String captainStatusOnline = 'online';
  static const String captainStatusBusy = 'busy';

  // Vehicle Types
  static const String vehicleTypeBicycle = 'bicycle';
  static const String vehicleTypeMotorcycle = 'motorcycle';
  static const String vehicleTypeCar = 'car';
  static const String vehicleTypeWalking = 'walking';

  // Payment Methods
  static const String paymentMethodCash = 'cash';
  static const String paymentMethodVodafoneCash = 'vodafone_cash';
  static const String paymentMethodFawry = 'fawry';
  static const String paymentMethodOrangeCash = 'orange_cash';
  static const String paymentMethodEtisalatCash = 'etisalat_cash';

  // Supported Languages
  static const List<Locale> supportedLocales = [Locale('ar'), Locale('en')];

  // Default Values
  static const String defaultLanguage = 'ar';
  static const String defaultCurrency = 'EGP';
  static const String defaultCountryCode = '+20';

  // Validation
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double defaultBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;

  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);

  // Network
  static const int connectionTimeout = 30000; // milliseconds
  static const int receiveTimeout = 30000; // milliseconds
  static const int sendTimeout = 30000; // milliseconds

  // Storage Keys
  static const String keyAccessToken = 'access_token';
  static const String keyRefreshToken = 'refresh_token';
  static const String keyUserData = 'user_data';
  static const String keyLanguage = 'language';
  static const String keyThemeMode = 'theme_mode';
  static const String keyNotificationsEnabled = 'notifications_enabled';
  static const String keyLocationPermissionGranted =
      'location_permission_granted';
  static const String keyOnboardingCompleted = 'onboarding_completed';

  // Error Messages
  static const String errorNetworkConnection = 'network_connection_error';
  static const String errorServerError = 'server_error';
  static const String errorUnknown = 'unknown_error';
  static const String errorValidation = 'validation_error';
  static const String errorUnauthorized = 'unauthorized_error';

  // Success Messages
  static const String successLogin = 'login_success';
  static const String successRegistration = 'registration_success';
  static const String successProfileUpdate = 'profile_update_success';
  static const String successOrderAccepted = 'order_accepted_success';
  static const String successOrderCompleted = 'order_completed_success';

  // Map Constants
  static const double defaultLatitude = 30.0444; // Cairo
  static const double defaultLongitude = 31.2357; // Cairo
  static const double mapZoom = 15.0;
  static const double trackingZoom = 18.0;

  // File Upload
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png'];

  // Notification Types
  static const String notificationTypeNewOrder = 'new_order';
  static const String notificationTypeOrderUpdate = 'order_update';
  static const String notificationTypeSystemMessage = 'system_message';
  static const String notificationTypePromotion = 'promotion';

  // Support Information
  static const String supportPhone = '+201234567890';
}
