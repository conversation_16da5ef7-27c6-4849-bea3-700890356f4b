import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../services/storage_service.dart';
import '../../features/splash/presentation/pages/splash_page.dart';
import '../../features/onboarding/presentation/pages/onboarding_page.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/register_page.dart';
import '../../features/auth/presentation/pages/user_type_selection_page.dart';
import '../../features/auth/presentation/pages/otp_verification_page.dart';
import '../../features/auth/presentation/pages/forgot_password_page.dart';
import '../../features/customer/presentation/pages/customer_main_page.dart';
import '../../features/captain/presentation/pages/captain_main_page.dart';

class AppRoutes {
  // Auth Routes
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String userTypeSelection = '/user-type-selection';
  static const String login = '/login';
  static const String register = '/register';
  static const String otpVerification = '/otp-verification';
  static const String forgotPassword = '/forgot-password';

  // Customer Routes
  static const String customerMain = '/customer';
  static const String customerHome = '/customer/home';
  static const String customerStores = '/customer/stores';
  static const String customerOrders = '/customer/orders';
  static const String customerProfile = '/customer/profile';
  static const String customerCart = '/customer/cart';
  static const String customerSearch = '/customer/search';

  // Captain Routes
  static const String captainMain = '/captain';
  static const String captainHome = '/captain/home';
  static const String captainOrders = '/captain/orders';
  static const String captainEarnings = '/captain/earnings';
  static const String captainProfile = '/captain/profile';
  static const String captainSettings = '/captain/settings';

  // Common Routes
  static const String notifications = '/notifications';
  static const String support = '/support';
  static const String about = '/about';
  static const String terms = '/terms';
  static const String privacy = '/privacy';
}

final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppRoutes.splash,
    debugLogDiagnostics: true,
    redirect: (context, state) async {
      final currentLocation = state.uri.toString();
      
      // Check if user is logged in
      final isLoggedIn = await StorageService.isUserLoggedIn();
      final userType = await StorageService.getUserType();
      
      // Check if onboarding is completed
      final onboardingCompleted = StorageService.isOnboardingCompleted();

      // If on splash page, determine where to redirect
      if (currentLocation == AppRoutes.splash) {
        if (!onboardingCompleted) {
          return AppRoutes.onboarding;
        } else if (!isLoggedIn) {
          return AppRoutes.userTypeSelection;
        } else {
          // Redirect based on user type
          if (userType == 'captain') {
            return AppRoutes.captainMain;
          } else {
            return AppRoutes.customerMain;
          }
        }
      }

      // If user is not logged in and trying to access protected routes
      if (!isLoggedIn && _isProtectedRoute(currentLocation)) {
        return AppRoutes.userTypeSelection;
      }

      // If user is logged in and trying to access auth routes
      if (isLoggedIn && _isAuthRoute(currentLocation)) {
        if (userType == 'captain') {
          return AppRoutes.captainMain;
        } else {
          return AppRoutes.customerMain;
        }
      }

      return null; // No redirect needed
    },
    routes: [
      // Splash Route
      GoRoute(
        path: AppRoutes.splash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // Onboarding Route
      GoRoute(
        path: AppRoutes.onboarding,
        name: 'onboarding',
        builder: (context, state) => const OnboardingPage(),
      ),

      // Auth Routes
      GoRoute(
        path: AppRoutes.userTypeSelection,
        name: 'user-type-selection',
        builder: (context, state) => const UserTypeSelectionPage(),
      ),

      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) {
          final userType = state.uri.queryParameters['userType'] ?? 'customer';
          return LoginPage(userType: userType);
        },
      ),

      GoRoute(
        path: AppRoutes.register,
        name: 'register',
        builder: (context, state) {
          final userType = state.uri.queryParameters['userType'] ?? 'customer';
          return RegisterPage(userType: userType);
        },
      ),

      GoRoute(
        path: AppRoutes.otpVerification,
        name: 'otp-verification',
        builder: (context, state) {
          final phone = state.uri.queryParameters['phone'] ?? '';
          final userType = state.uri.queryParameters['userType'] ?? 'customer';
          return OtpVerificationPage(phone: phone, userType: userType);
        },
      ),

      GoRoute(
        path: AppRoutes.forgotPassword,
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordPage(),
      ),

      // Customer Routes
      GoRoute(
        path: AppRoutes.customerMain,
        name: 'customer-main',
        builder: (context, state) => const CustomerMainPage(),
        routes: [
          GoRoute(
            path: 'home',
            name: 'customer-home',
            builder: (context, state) => const CustomerMainPage(initialIndex: 0),
          ),
          GoRoute(
            path: 'stores',
            name: 'customer-stores',
            builder: (context, state) => const CustomerMainPage(initialIndex: 1),
          ),
          GoRoute(
            path: 'orders',
            name: 'customer-orders',
            builder: (context, state) => const CustomerMainPage(initialIndex: 2),
          ),
          GoRoute(
            path: 'profile',
            name: 'customer-profile',
            builder: (context, state) => const CustomerMainPage(initialIndex: 3),
          ),
        ],
      ),

      // Captain Routes
      GoRoute(
        path: AppRoutes.captainMain,
        name: 'captain-main',
        builder: (context, state) => const CaptainMainPage(),
        routes: [
          GoRoute(
            path: 'home',
            name: 'captain-home',
            builder: (context, state) => const CaptainMainPage(initialIndex: 0),
          ),
          GoRoute(
            path: 'orders',
            name: 'captain-orders',
            builder: (context, state) => const CaptainMainPage(initialIndex: 1),
          ),
          GoRoute(
            path: 'earnings',
            name: 'captain-earnings',
            builder: (context, state) => const CaptainMainPage(initialIndex: 2),
          ),
          GoRoute(
            path: 'profile',
            name: 'captain-profile',
            builder: (context, state) => const CaptainMainPage(initialIndex: 3),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'صفحة غير موجودة',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'المسار المطلوب غير متاح',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.splash),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
  );
});

bool _isProtectedRoute(String route) {
  const protectedRoutes = [
    AppRoutes.customerMain,
    AppRoutes.captainMain,
  ];
  
  return protectedRoutes.any((protectedRoute) => 
      route.startsWith(protectedRoute));
}

bool _isAuthRoute(String route) {
  const authRoutes = [
    AppRoutes.userTypeSelection,
    AppRoutes.login,
    AppRoutes.register,
    AppRoutes.otpVerification,
    AppRoutes.forgotPassword,
  ];
  
  return authRoutes.contains(route);
}
