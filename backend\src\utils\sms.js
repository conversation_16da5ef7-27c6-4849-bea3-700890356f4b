const axios = require('axios');
const logger = require('./logger');

// SMS service configuration
const SMS_CONFIG = {
  apiKey: process.env.SMS_API_KEY,
  senderId: process.env.SMS_SENDER_ID || 'TRAGO',
  baseUrl: process.env.SMS_BASE_URL || 'https://api.sms-provider.com/v1'
};

// Send OTP SMS
const sendOTP = async (phone, otp) => {
  try {
    if (!SMS_CONFIG.apiKey) {
      logger.warn('SMS API key not configured, skipping SMS send');
      return { success: true, message: 'SMS service not configured' };
    }

    // Format phone number (remove leading 0 and add +20)
    const formattedPhone = phone.startsWith('0') ? `+2${phone}` : `+20${phone}`;
    
    // OTP message in Arabic and English
    const message = `رمز التحقق الخاص بك في تراجو: ${otp}\nYour Trago verification code: ${otp}`;

    const response = await axios.post(`${SMS_CONFIG.baseUrl}/send`, {
      to: formattedPhone,
      from: SMS_CONFIG.senderId,
      message: message,
      api_key: SMS_CONFIG.apiKey
    }, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SMS_CONFIG.apiKey}`
      }
    });

    if (response.data.success) {
      logger.info(`OTP SMS sent successfully to ${formattedPhone}`);
      return { success: true, messageId: response.data.messageId };
    } else {
      logger.error(`Failed to send OTP SMS: ${response.data.message}`);
      throw new Error(response.data.message || 'SMS sending failed');
    }

  } catch (error) {
    logger.error('SMS sending error:', error.message);
    
    // In development, log the OTP for testing
    if (process.env.NODE_ENV === 'development') {
      logger.info(`Development OTP for ${phone}: ${otp}`);
      return { success: true, message: 'Development mode - OTP logged' };
    }
    
    throw new Error('Failed to send SMS');
  }
};

// Send order notification SMS
const sendOrderNotification = async (phone, orderNumber, status) => {
  try {
    if (!SMS_CONFIG.apiKey) {
      logger.warn('SMS API key not configured, skipping SMS send');
      return { success: true, message: 'SMS service not configured' };
    }

    const formattedPhone = phone.startsWith('0') ? `+2${phone}` : `+20${phone}`;
    
    // Status messages in Arabic
    const statusMessages = {
      confirmed: 'تم تأكيد طلبك',
      preparing: 'جاري تحضير طلبك',
      ready: 'طلبك جاهز للاستلام',
      picked_up: 'تم استلام طلبك من المتجر',
      on_the_way: 'طلبك في الطريق إليك',
      delivered: 'تم توصيل طلبك بنجاح',
      cancelled: 'تم إلغاء طلبك'
    };

    const statusMessage = statusMessages[status] || 'تم تحديث حالة طلبك';
    const message = `تراجو: ${statusMessage} رقم ${orderNumber}. شكراً لاختيارك تراجو!`;

    const response = await axios.post(`${SMS_CONFIG.baseUrl}/send`, {
      to: formattedPhone,
      from: SMS_CONFIG.senderId,
      message: message,
      api_key: SMS_CONFIG.apiKey
    }, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SMS_CONFIG.apiKey}`
      }
    });

    if (response.data.success) {
      logger.info(`Order notification SMS sent to ${formattedPhone} for order ${orderNumber}`);
      return { success: true, messageId: response.data.messageId };
    } else {
      logger.error(`Failed to send order notification SMS: ${response.data.message}`);
      throw new Error(response.data.message || 'SMS sending failed');
    }

  } catch (error) {
    logger.error('Order notification SMS error:', error.message);
    throw new Error('Failed to send order notification SMS');
  }
};

// Send captain notification SMS
const sendCaptainNotification = async (phone, message) => {
  try {
    if (!SMS_CONFIG.apiKey) {
      logger.warn('SMS API key not configured, skipping SMS send');
      return { success: true, message: 'SMS service not configured' };
    }

    const formattedPhone = phone.startsWith('0') ? `+2${phone}` : `+20${phone}`;
    
    const fullMessage = `تراجو كابتن: ${message}`;

    const response = await axios.post(`${SMS_CONFIG.baseUrl}/send`, {
      to: formattedPhone,
      from: SMS_CONFIG.senderId,
      message: fullMessage,
      api_key: SMS_CONFIG.apiKey
    }, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SMS_CONFIG.apiKey}`
      }
    });

    if (response.data.success) {
      logger.info(`Captain notification SMS sent to ${formattedPhone}`);
      return { success: true, messageId: response.data.messageId };
    } else {
      logger.error(`Failed to send captain notification SMS: ${response.data.message}`);
      throw new Error(response.data.message || 'SMS sending failed');
    }

  } catch (error) {
    logger.error('Captain notification SMS error:', error.message);
    throw new Error('Failed to send captain notification SMS');
  }
};

// Verify OTP (placeholder for future SMS provider integration)
const verifyOTP = async (phone, otp) => {
  try {
    // This would typically verify with the SMS provider
    // For now, we'll rely on our own OTP storage in Redis
    logger.info(`OTP verification requested for ${phone}: ${otp}`);
    return { success: true, verified: true };
  } catch (error) {
    logger.error('OTP verification error:', error.message);
    return { success: false, verified: false };
  }
};

// Get SMS delivery status
const getSMSStatus = async (messageId) => {
  try {
    if (!SMS_CONFIG.apiKey || !messageId) {
      return { success: false, message: 'Invalid parameters' };
    }

    const response = await axios.get(`${SMS_CONFIG.baseUrl}/status/${messageId}`, {
      headers: {
        'Authorization': `Bearer ${SMS_CONFIG.apiKey}`
      },
      timeout: 5000
    });

    return {
      success: true,
      status: response.data.status,
      deliveredAt: response.data.deliveredAt
    };

  } catch (error) {
    logger.error('SMS status check error:', error.message);
    return { success: false, message: 'Failed to get SMS status' };
  }
};

// Format phone number for international format
const formatPhoneNumber = (phone) => {
  // Remove any non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Handle Egyptian phone numbers
  if (cleanPhone.startsWith('20')) {
    return `+${cleanPhone}`;
  } else if (cleanPhone.startsWith('0')) {
    return `+2${cleanPhone}`;
  } else if (cleanPhone.length === 10) {
    return `+20${cleanPhone}`;
  }
  
  return `+20${cleanPhone}`;
};

// Validate Egyptian phone number
const isValidEgyptianPhone = (phone) => {
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Check if it's a valid Egyptian mobile number
  const egyptianMobileRegex = /^(01[0-2,5]{1}[0-9]{8})$/;
  
  if (cleanPhone.startsWith('20')) {
    return egyptianMobileRegex.test(cleanPhone.substring(2));
  } else if (cleanPhone.startsWith('0')) {
    return egyptianMobileRegex.test(cleanPhone);
  } else if (cleanPhone.length === 10) {
    return egyptianMobileRegex.test(`0${cleanPhone}`);
  }
  
  return false;
};

module.exports = {
  sendOTP,
  sendOrderNotification,
  sendCaptainNotification,
  verifyOTP,
  getSMSStatus,
  formatPhoneNumber,
  isValidEgyptianPhone
};
