import 'package:dio/dio.dart';
import '../config/app_config.dart';
import '../models/captain_model.dart';
import '../models/order_model.dart';
import 'storage_service.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  late Dio _dio;
  final StorageService _storage = StorageService();

  void initialize() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.apiUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add auth token if available
        final token = await _storage.getAccessToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        // Handle token refresh on 401
        if (error.response?.statusCode == 401) {
          final refreshed = await _refreshToken();
          if (refreshed) {
            // Retry the original request
            final token = await _storage.getAccessToken();
            error.requestOptions.headers['Authorization'] = 'Bearer $token';
            final response = await _dio.fetch(error.requestOptions);
            handler.resolve(response);
            return;
          }
        }
        handler.next(error);
      },
    ));

    if (AppConfig.enableNetworkLogging) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: false,
      ));
    }
  }

  // Auth Methods
  Future<ApiResponse<CaptainModel>> login({
    required String phone,
    required String password,
  }) async {
    try {
      final response = await _dio.post('/captain/login', data: {
        'phone': phone,
        'password': password,
      });

      if (response.data['success']) {
        final captain = CaptainModel.fromJson(response.data['data']['captain']);
        final token = response.data['data']['token'];
        
        await _storage.saveAccessToken(token);
        await _storage.saveCaptainData(captain);
        
        return ApiResponse.success(captain);
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Login failed');
      }
    } on DioException catch (e) {
      return ApiResponse.error(_handleDioError(e));
    }
  }

  Future<ApiResponse<CaptainModel>> register({
    required String name,
    required String phone,
    required String email,
    required String password,
    required String vehicleType,
    required String vehicleNumber,
    String? vehicleModel,
    String? vehicleColor,
  }) async {
    try {
      final response = await _dio.post('/captain/register', data: {
        'name': name,
        'phone': phone,
        'email': email,
        'password': password,
        'vehicle_type': vehicleType,
        'vehicle_number': vehicleNumber,
        'vehicle_model': vehicleModel,
        'vehicle_color': vehicleColor,
      });

      if (response.data['success']) {
        final captain = CaptainModel.fromJson(response.data['data']);
        return ApiResponse.success(captain);
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Registration failed');
      }
    } on DioException catch (e) {
      return ApiResponse.error(_handleDioError(e));
    }
  }

  Future<bool> logout() async {
    try {
      await _dio.post('/captain/logout');
      await _storage.clearAll();
      return true;
    } catch (e) {
      await _storage.clearAll();
      return true;
    }
  }

  // Captain Methods
  Future<ApiResponse<CaptainModel>> getProfile() async {
    try {
      final response = await _dio.get('/captain/profile');
      
      if (response.data['success']) {
        final captain = CaptainModel.fromJson(response.data['data']);
        await _storage.saveCaptainData(captain);
        return ApiResponse.success(captain);
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to get profile');
      }
    } on DioException catch (e) {
      return ApiResponse.error(_handleDioError(e));
    }
  }

  Future<ApiResponse<bool>> updateStatus(String status) async {
    try {
      final response = await _dio.put('/captain/status', data: {
        'status': status,
      });

      if (response.data['success']) {
        return ApiResponse.success(true);
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to update status');
      }
    } on DioException catch (e) {
      return ApiResponse.error(_handleDioError(e));
    }
  }

  Future<ApiResponse<bool>> updateLocation({
    required double latitude,
    required double longitude,
  }) async {
    try {
      final response = await _dio.put('/captain/location', data: {
        'latitude': latitude,
        'longitude': longitude,
      });

      if (response.data['success']) {
        return ApiResponse.success(true);
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to update location');
      }
    } on DioException catch (e) {
      return ApiResponse.error(_handleDioError(e));
    }
  }

  // Order Methods
  Future<ApiResponse<List<OrderModel>>> getAvailableOrders() async {
    try {
      final response = await _dio.get('/captain/orders/available');
      
      if (response.data['success']) {
        final orders = (response.data['data'] as List)
            .map((order) => OrderModel.fromJson(order))
            .toList();
        return ApiResponse.success(orders);
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to get orders');
      }
    } on DioException catch (e) {
      return ApiResponse.error(_handleDioError(e));
    }
  }

  Future<ApiResponse<List<OrderModel>>> getMyOrders() async {
    try {
      final response = await _dio.get('/captain/orders/my');
      
      if (response.data['success']) {
        final orders = (response.data['data'] as List)
            .map((order) => OrderModel.fromJson(order))
            .toList();
        return ApiResponse.success(orders);
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to get orders');
      }
    } on DioException catch (e) {
      return ApiResponse.error(_handleDioError(e));
    }
  }

  Future<ApiResponse<bool>> acceptOrder(String orderId) async {
    try {
      final response = await _dio.post('/captain/orders/$orderId/accept');

      if (response.data['success']) {
        return ApiResponse.success(true);
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to accept order');
      }
    } on DioException catch (e) {
      return ApiResponse.error(_handleDioError(e));
    }
  }

  Future<ApiResponse<bool>> updateOrderStatus({
    required String orderId,
    required String status,
  }) async {
    try {
      final response = await _dio.put('/captain/orders/$orderId/status', data: {
        'status': status,
      });

      if (response.data['success']) {
        return ApiResponse.success(true);
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to update order status');
      }
    } on DioException catch (e) {
      return ApiResponse.error(_handleDioError(e));
    }
  }

  // Helper Methods
  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await _storage.getRefreshToken();
      if (refreshToken == null) return false;

      final response = await _dio.post('/auth/refresh', data: {
        'refresh_token': refreshToken,
      });

      if (response.data['success']) {
        final newToken = response.data['data']['access_token'];
        await _storage.saveAccessToken(newToken);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  String _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data?['message'];
        return message ?? 'Server error ($statusCode)';
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.unknown:
        return 'Network error. Please check your internet connection.';
      default:
        return 'An unexpected error occurred';
    }
  }
}

class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? error;

  ApiResponse.success(this.data) : success = true, error = null;
  ApiResponse.error(this.error) : success = false, data = null;
}
