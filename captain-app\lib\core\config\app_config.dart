class AppConfig {
  // App Information
  static const String appName = 'Trago Captain';
  static const String appNameArabic = 'تراجو كابتن';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق الكابتن لتوصيل الطلبات';
  static const String appDescriptionEn = 'Captain App for Order Delivery';

  // Environment Configuration
  static const String environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );

  // API Configuration
  static const String baseUrl = String.fromEnvironment(
    'BASE_URL',
    defaultValue: 'http://localhost:3001',
  );
  
  static const String apiVersion = 'v1';
  static String get apiUrl => '$baseUrl/api';
  
  // Socket Configuration
  static String get socketUrl => baseUrl;
  
  // Feature Flags
  static const bool enableLogging = true;
  static const bool enableNetworkLogging = true;
  static const bool enableLocationTracking = true;
  static const bool enableNotifications = true;
  static const bool enableCrashReporting = false;
  static const bool enableAnalytics = false;

  // Location Configuration
  static const double locationAccuracy = 10.0; // meters
  static const int locationUpdateInterval = 5000; // milliseconds
  static const int locationFastestInterval = 2000; // milliseconds

  // Order Configuration
  static const int orderRefreshInterval = 30; // seconds
  static const double maxDeliveryDistance = 50.0; // kilometers
  static const int orderTimeoutMinutes = 30;

  // Map Configuration
  static const double defaultZoom = 15.0;
  static const double trackingZoom = 18.0;

  // Support Information
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+201234567890';
  static const String privacyPolicyUrl = 'https://trago-app.com/captain-privacy';
  static const String termsOfServiceUrl = 'https://trago-app.com/captain-terms';

  // Utility Methods
  static String getEnvironmentName() {
    switch (environment) {
      case 'production':
        return 'Production';
      case 'staging':
        return 'Staging';
      default:
        return 'Development';
    }
  }

  static bool get isProduction => environment == 'production';
  static bool get isDevelopment => environment == 'development';
  static bool get isStaging => environment == 'staging';

  static Map<String, dynamic> getConfigInfo() {
    return {
      'environment': environment,
      'baseUrl': baseUrl,
      'apiUrl': apiUrl,
      'socketUrl': socketUrl,
      'enableLogging': enableLogging,
      'enableNetworkLogging': enableNetworkLogging,
      'enableLocationTracking': enableLocationTracking,
      'enableNotifications': enableNotifications,
      'enableCrashReporting': enableCrashReporting,
      'enableAnalytics': enableAnalytics,
    };
  }
}
