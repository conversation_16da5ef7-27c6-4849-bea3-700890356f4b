// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'تراجو';

  @override
  String get welcome => 'مرحباً';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get register => 'إنشاء حساب';

  @override
  String get phoneNumber => 'رقم الهاتف';

  @override
  String get password => 'كلمة المرور';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get name => 'الاسم';

  @override
  String get customer => 'عميل';

  @override
  String get captain => 'كابتن';

  @override
  String get home => 'الرئيسية';

  @override
  String get orders => 'الطلبات';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get settings => 'الإعدادات';

  @override
  String get stores => 'المتاجر';

  @override
  String get cart => 'السلة';

  @override
  String get search => 'البحث';

  @override
  String get earnings => 'الأرباح';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get forgotPassword => 'نسيت كلمة المرور؟';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get acceptTerms => 'أوافق على الشروط والأحكام';

  @override
  String get alreadyHaveAccount => 'لديك حساب بالفعل؟';

  @override
  String get dontHaveAccount => 'ليس لديك حساب؟';

  @override
  String get createAccount => 'إنشاء حساب';

  @override
  String get verifyPhone => 'تحقق من رقم الهاتف';

  @override
  String get enterOtpCode => 'أدخل الرمز المرسل إلى';

  @override
  String get resendCode => 'إعادة إرسال الرمز';

  @override
  String get verify => 'تحقق';

  @override
  String get vehicleType => 'نوع المركبة';

  @override
  String get vehicleNumber => 'رقم المركبة';

  @override
  String get motorcycle => 'موتوسيكل';

  @override
  String get car => 'سيارة';

  @override
  String get bicycle => 'دراجة هوائية';

  @override
  String get walking => 'مشي';

  @override
  String get online => 'متصل';

  @override
  String get offline => 'غير متصل';

  @override
  String get busy => 'مشغول';
}
