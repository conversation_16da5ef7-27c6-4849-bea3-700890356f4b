# Trago Unified App - تطبيق تراجو الموحد

تطبيق Flutter موحد يجمع بين وظائف العملاء والكباتن في تطبيق واحد لخدمة توصيل الطلبات.

## المميزات الرئيسية

### للعملاء
- تصفح المتاجر والمطاعم
- إضافة المنتجات للسلة
- تتبع الطلبات في الوقت الفعلي
- إدارة العناوين المفضلة
- تقييم الطلبات والكباتن

### للكباتن
- استقبال طلبات التوصيل
- تتبع الأرباح اليومية والشهرية
- إدارة حالة الاتصال (متصل/غير متصل)
- تحديث الموقع الجغرافي
- إدارة معلومات المركبة

## البنية التقنية

### التقنيات المستخدمة
- **Flutter**: إطار العمل الأساسي
- **Riverpod**: إدارة الحالة
- **GoRouter**: التنقل والتوجيه
- **Dio**: طلبات HTTP
- **Hive**: التخزين المحلي
- **Firebase**: الإشعارات والمصادقة
- **Google Maps**: الخرائط والموقع

### بنية المشروع
```
lib/
├── core/                    # الملفات الأساسية المشتركة
│   ├── config/             # إعدادات التطبيق
│   ├── models/             # نماذج البيانات
│   ├── services/           # الخدمات (API, Storage, etc.)
│   ├── theme/              # الألوان والثيمات
│   ├── utils/              # الأدوات المساعدة
│   └── providers/          # مقدمي الخدمة العامة
├── features/               # الميزات الرئيسية
│   ├── auth/               # المصادقة والتسجيل
│   ├── customer/           # ميزات العملاء
│   ├── captain/            # ميزات الكباتن
│   ├── splash/             # شاشة البداية
│   └── onboarding/         # التعريف بالتطبيق
└── generated/              # الملفات المولدة تلقائياً
```

## نظام المصادقة الموحد

### تدفق التسجيل
1. **اختيار نوع المستخدم**: عميل أو كابتن
2. **إدخال البيانات**: الاسم، الهاتف، البريد الإلكتروني، كلمة المرور
3. **معلومات إضافية للكباتن**: نوع المركبة، رقم اللوحة
4. **التحقق من OTP**: رمز التحقق عبر الرسائل النصية
5. **تفعيل الحساب**: الانتقال للواجهة المناسبة

### تدفق تسجيل الدخول
1. **اختيار نوع المستخدم**: من الشاشة الرئيسية أو القائمة
2. **إدخال البيانات**: رقم الهاتف وكلمة المرور
3. **التحقق**: مقارنة البيانات مع الخادم
4. **التوجيه**: انتقال تلقائي للواجهة المناسبة

## إعداد المشروع

### المتطلبات
- Flutter SDK (3.0.0 أو أحدث)
- Dart SDK (2.17.0 أو أحدث)
- Android Studio / VS Code
- Firebase Project

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd trago_unified_app
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **إعداد Firebase**
```bash
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# إعداد المشروع
flutterfire configure
```

4. **تشغيل التطبيق**
```bash
flutter run
```

## إعداد البيئة

### متغيرات البيئة
قم بإنشاء ملف `.env` في جذر المشروع:
```
API_BASE_URL=https://api.trago-app.com
SOCKET_URL=https://api.trago-app.com
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
```

### إعدادات Firebase
1. إنشاء مشروع Firebase جديد
2. تفعيل Authentication و Cloud Messaging
3. إضافة التطبيق للمنصات (Android/iOS)
4. تحديث ملف `firebase_options.dart`

## البناء والنشر

### بناء التطبيق للإنتاج

**Android:**
```bash
flutter build apk --release
# أو
flutter build appbundle --release
```

**iOS:**
```bash
flutter build ios --release
```

### اختبار التطبيق
```bash
# تشغيل الاختبارات
flutter test

# تحليل الكود
flutter analyze

# فحص الأداء
flutter run --profile
```

## المساهمة

### قواعد المساهمة
1. اتبع معايير Dart/Flutter الرسمية
2. اكتب اختبارات للميزات الجديدة
3. استخدم التعليقات باللغة العربية للواجهات
4. اتبع بنية المشروع الحالية

### تقديم التحسينات
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات مع رسائل واضحة
4. Push للـ branch
5. إنشاء Pull Request

## الدعم والمساعدة

### المشاكل الشائعة
- **مشكلة في Firebase**: تأكد من إعداد المشروع بشكل صحيح
- **مشكلة في الخرائط**: تحقق من صحة Google Maps API Key
- **مشكلة في البناء**: قم بتشغيل `flutter clean` ثم `flutter pub get`

### التواصل
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: https://trago-app.com

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الإصدارات

### الإصدار 1.0.0
- نظام مصادقة موحد
- واجهات منفصلة للعملاء والكباتن
- تكامل مع Firebase
- دعم اللغة العربية
- نظام إشعارات متقدم
