import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel {
  final String id;
  final String phone;
  final String? email;
  final String name;
  final String? avatar;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_type')
  final String userType;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_verified')
  final bool isVerified;
  @Json<PERSON>ey(name: 'is_active')
  final bool isActive;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @<PERSON>son<PERSON>ey(name: 'updated_at')
  final DateTime updatedAt;
  @<PERSON><PERSON><PERSON>ey(name: 'last_login')
  final DateTime? lastLogin;

  // Captain-specific fields
  @Json<PERSON>ey(name: 'captain_data')
  final CaptainData? captainData;

  // Customer-specific fields
  @Json<PERSON>ey(name: 'customer_data')
  final CustomerData? customerData;

  const UserModel({
    required this.id,
    required this.phone,
    this.email,
    required this.name,
    this.avatar,
    required this.userType,
    required this.isVerified,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.lastLogin,
    this.captainData,
    this.customerData,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  bool get isCaptain => userType == 'captain';
  bool get isCustomer => userType == 'customer';
  bool get isStoreOwner => userType == 'store_owner';
  bool get isAdmin => userType == 'admin';

  UserModel copyWith({
    String? id,
    String? phone,
    String? email,
    String? name,
    String? avatar,
    String? userType,
    bool? isVerified,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLogin,
    CaptainData? captainData,
    CustomerData? customerData,
  }) {
    return UserModel(
      id: id ?? this.id,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      userType: userType ?? this.userType,
      isVerified: isVerified ?? this.isVerified,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLogin: lastLogin ?? this.lastLogin,
      captainData: captainData ?? this.captainData,
      customerData: customerData ?? this.customerData,
    );
  }

  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, phone: $phone, userType: $userType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

@JsonSerializable()
class CaptainData {
  @JsonKey(name: 'license_number')
  final String? licenseNumber;
  @JsonKey(name: 'vehicle_type')
  final String vehicleType;
  @JsonKey(name: 'vehicle_plate')
  final String? vehiclePlate;
  @JsonKey(name: 'vehicle_model')
  final String? vehicleModel;
  @JsonKey(name: 'vehicle_color')
  final String? vehicleColor;
  @JsonKey(name: 'is_approved')
  final bool isApproved;
  final String status;
  @JsonKey(name: 'current_location')
  final LocationData? currentLocation;
  final double rating;
  @JsonKey(name: 'total_orders')
  final int totalOrders;
  @JsonKey(name: 'completed_orders')
  final int completedOrders;
  @JsonKey(name: 'cancelled_orders')
  final int cancelledOrders;
  @JsonKey(name: 'total_earnings')
  final double totalEarnings;
  final List<String>? documents;
  @JsonKey(name: 'approved_at')
  final DateTime? approvedAt;

  const CaptainData({
    this.licenseNumber,
    required this.vehicleType,
    this.vehiclePlate,
    this.vehicleModel,
    this.vehicleColor,
    required this.isApproved,
    required this.status,
    this.currentLocation,
    required this.rating,
    required this.totalOrders,
    required this.completedOrders,
    required this.cancelledOrders,
    required this.totalEarnings,
    this.documents,
    this.approvedAt,
  });

  factory CaptainData.fromJson(Map<String, dynamic> json) =>
      _$CaptainDataFromJson(json);

  Map<String, dynamic> toJson() => _$CaptainDataToJson(this);

  bool get isOnline => status == 'online';
  bool get isOffline => status == 'offline';
  bool get isBusy => status == 'busy';

  CaptainData copyWith({
    String? licenseNumber,
    String? vehicleType,
    String? vehiclePlate,
    String? vehicleModel,
    String? vehicleColor,
    bool? isApproved,
    String? status,
    LocationData? currentLocation,
    double? rating,
    int? totalOrders,
    int? completedOrders,
    int? cancelledOrders,
    double? totalEarnings,
    List<String>? documents,
    DateTime? approvedAt,
  }) {
    return CaptainData(
      licenseNumber: licenseNumber ?? this.licenseNumber,
      vehicleType: vehicleType ?? this.vehicleType,
      vehiclePlate: vehiclePlate ?? this.vehiclePlate,
      vehicleModel: vehicleModel ?? this.vehicleModel,
      vehicleColor: vehicleColor ?? this.vehicleColor,
      isApproved: isApproved ?? this.isApproved,
      status: status ?? this.status,
      currentLocation: currentLocation ?? this.currentLocation,
      rating: rating ?? this.rating,
      totalOrders: totalOrders ?? this.totalOrders,
      completedOrders: completedOrders ?? this.completedOrders,
      cancelledOrders: cancelledOrders ?? this.cancelledOrders,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      documents: documents ?? this.documents,
      approvedAt: approvedAt ?? this.approvedAt,
    );
  }
}

@JsonSerializable()
class CustomerData {
  @JsonKey(name: 'delivery_addresses')
  final List<AddressData>? deliveryAddresses;
  @JsonKey(name: 'favorite_stores')
  final List<String>? favoriteStores;
  @JsonKey(name: 'total_orders')
  final int totalOrders;
  @JsonKey(name: 'total_spent')
  final double totalSpent;
  @JsonKey(name: 'loyalty_points')
  final int loyaltyPoints;
  @JsonKey(name: 'preferred_payment_method')
  final String? preferredPaymentMethod;

  const CustomerData({
    this.deliveryAddresses,
    this.favoriteStores,
    required this.totalOrders,
    required this.totalSpent,
    required this.loyaltyPoints,
    this.preferredPaymentMethod,
  });

  factory CustomerData.fromJson(Map<String, dynamic> json) =>
      _$CustomerDataFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerDataToJson(this);

  CustomerData copyWith({
    List<AddressData>? deliveryAddresses,
    List<String>? favoriteStores,
    int? totalOrders,
    double? totalSpent,
    int? loyaltyPoints,
    String? preferredPaymentMethod,
  }) {
    return CustomerData(
      deliveryAddresses: deliveryAddresses ?? this.deliveryAddresses,
      favoriteStores: favoriteStores ?? this.favoriteStores,
      totalOrders: totalOrders ?? this.totalOrders,
      totalSpent: totalSpent ?? this.totalSpent,
      loyaltyPoints: loyaltyPoints ?? this.loyaltyPoints,
      preferredPaymentMethod: preferredPaymentMethod ?? this.preferredPaymentMethod,
    );
  }
}

@JsonSerializable()
class LocationData {
  final double latitude;
  final double longitude;
  final String? address;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const LocationData({
    required this.latitude,
    required this.longitude,
    this.address,
    required this.updatedAt,
  });

  factory LocationData.fromJson(Map<String, dynamic> json) =>
      _$LocationDataFromJson(json);

  Map<String, dynamic> toJson() => _$LocationDataToJson(this);
}

@JsonSerializable()
class AddressData {
  final String id;
  final String title;
  final String address;
  final double latitude;
  final double longitude;
  @JsonKey(name: 'is_default')
  final bool isDefault;

  const AddressData({
    required this.id,
    required this.title,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.isDefault,
  });

  factory AddressData.fromJson(Map<String, dynamic> json) =>
      _$AddressDataFromJson(json);

  Map<String, dynamic> toJson() => _$AddressDataToJson(this);
}
