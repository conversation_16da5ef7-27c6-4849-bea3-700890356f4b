# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/trago_db
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# Google Maps API
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY=your-firebase-private-key
FIREBASE_CLIENT_EMAIL=your-firebase-client-email

# Payment Gateways
# Vodafone Cash
VODAFONE_CASH_MERCHANT_ID=your-vodafone-merchant-id
VODAFONE_CASH_SECRET=your-vodafone-secret

# Fawry
FAWRY_MERCHANT_CODE=your-fawry-merchant-code
FAWRY_SECURITY_KEY=your-fawry-security-key

# Orange Cash
ORANGE_CASH_MERCHANT_ID=your-orange-merchant-id
ORANGE_CASH_SECRET=your-orange-secret

# Etisalat Cash
ETISALAT_CASH_MERCHANT_ID=your-etisalat-merchant-id
ETISALAT_CASH_SECRET=your-etisalat-secret

# File Upload
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# SMS Configuration (for OTP)
SMS_API_KEY=your-sms-api-key
SMS_SENDER_ID=TRAGO

# App Configuration
APP_NAME=Trago
APP_URL=https://trago-app.com
ADMIN_URL=https://admin.trago-app.com
API_URL=https://api.trago-app.com

# Environment
NODE_ENV=development
PORT=3000
ADMIN_PORT=3001

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Delivery Configuration
DEFAULT_DELIVERY_FEE=15
MAX_DELIVERY_DISTANCE_KM=20
DELIVERY_TIME_ESTIMATE_MINUTES=30
