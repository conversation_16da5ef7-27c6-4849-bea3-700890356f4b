import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../models/captain_model.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  SharedPreferences? _prefs;

  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('StorageService not initialized. Call initialize() first.');
    }
    return _prefs!;
  }

  // Token Management
  Future<void> saveAccessToken(String token) async {
    await prefs.setString(AppConstants.keyAccessToken, token);
  }

  Future<String?> getAccessToken() async {
    return prefs.getString(AppConstants.keyAccessToken);
  }

  Future<void> saveRefreshToken(String token) async {
    await prefs.setString(AppConstants.keyRefreshToken, token);
  }

  Future<String?> getRefreshToken() async {
    return prefs.getString(AppConstants.keyRefreshToken);
  }

  Future<void> clearTokens() async {
    await prefs.remove(AppConstants.keyAccessToken);
    await prefs.remove(AppConstants.keyRefreshToken);
  }

  // Captain Data Management
  Future<void> saveCaptainData(CaptainModel captain) async {
    final captainJson = jsonEncode(captain.toJson());
    await prefs.setString(AppConstants.keyUserData, captainJson);
  }

  Future<CaptainModel?> getCaptainData() async {
    final captainJson = prefs.getString(AppConstants.keyUserData);
    if (captainJson != null) {
      try {
        final captainMap = jsonDecode(captainJson) as Map<String, dynamic>;
        return CaptainModel.fromJson(captainMap);
      } catch (e) {
        // If parsing fails, remove corrupted data
        await prefs.remove(AppConstants.keyUserData);
        return null;
      }
    }
    return null;
  }

  Future<void> clearCaptainData() async {
    await prefs.remove(AppConstants.keyUserData);
  }

  // App Settings
  Future<void> setLanguage(String languageCode) async {
    await prefs.setString(AppConstants.keyLanguage, languageCode);
  }

  Future<String> getLanguage() async {
    return prefs.getString(AppConstants.keyLanguage) ?? AppConstants.defaultLanguage;
  }

  Future<void> setThemeMode(String themeMode) async {
    await prefs.setString(AppConstants.keyThemeMode, themeMode);
  }

  Future<String> getThemeMode() async {
    return prefs.getString(AppConstants.keyThemeMode) ?? 'system';
  }

  // Notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    await prefs.setBool(AppConstants.keyNotificationsEnabled, enabled);
  }

  Future<bool> getNotificationsEnabled() async {
    return prefs.getBool(AppConstants.keyNotificationsEnabled) ?? true;
  }

  // Location Permission
  Future<void> setLocationPermissionGranted(bool granted) async {
    await prefs.setBool(AppConstants.keyLocationPermissionGranted, granted);
  }

  Future<bool> getLocationPermissionGranted() async {
    return prefs.getBool(AppConstants.keyLocationPermissionGranted) ?? false;
  }

  // Onboarding
  Future<void> setOnboardingCompleted(bool completed) async {
    await prefs.setBool(AppConstants.keyOnboardingCompleted, completed);
  }

  Future<bool> getOnboardingCompleted() async {
    return prefs.getBool(AppConstants.keyOnboardingCompleted) ?? false;
  }

  // Captain Status
  Future<void> saveLastCaptainStatus(String status) async {
    await prefs.setString('last_captain_status', status);
  }

  Future<String> getLastCaptainStatus() async {
    return prefs.getString('last_captain_status') ?? AppConstants.captainStatusOffline;
  }

  // Location Data
  Future<void> saveLastLocation({
    required double latitude,
    required double longitude,
  }) async {
    await prefs.setDouble('last_latitude', latitude);
    await prefs.setDouble('last_longitude', longitude);
    await prefs.setInt('last_location_timestamp', DateTime.now().millisecondsSinceEpoch);
  }

  Future<Map<String, double>?> getLastLocation() async {
    final latitude = prefs.getDouble('last_latitude');
    final longitude = prefs.getDouble('last_longitude');
    
    if (latitude != null && longitude != null) {
      return {
        'latitude': latitude,
        'longitude': longitude,
      };
    }
    return null;
  }

  Future<DateTime?> getLastLocationTimestamp() async {
    final timestamp = prefs.getInt('last_location_timestamp');
    if (timestamp != null) {
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    }
    return null;
  }

  // App State
  Future<void> saveAppState(Map<String, dynamic> state) async {
    final stateJson = jsonEncode(state);
    await prefs.setString('app_state', stateJson);
  }

  Future<Map<String, dynamic>?> getAppState() async {
    final stateJson = prefs.getString('app_state');
    if (stateJson != null) {
      try {
        return jsonDecode(stateJson) as Map<String, dynamic>;
      } catch (e) {
        await prefs.remove('app_state');
        return null;
      }
    }
    return null;
  }

  // Statistics
  Future<void> incrementOrdersCompleted() async {
    final current = prefs.getInt('orders_completed_count') ?? 0;
    await prefs.setInt('orders_completed_count', current + 1);
  }

  Future<int> getOrdersCompletedCount() async {
    return prefs.getInt('orders_completed_count') ?? 0;
  }

  Future<void> addEarnings(double amount) async {
    final current = prefs.getDouble('total_earnings') ?? 0.0;
    await prefs.setDouble('total_earnings', current + amount);
  }

  Future<double> getTotalEarnings() async {
    return prefs.getDouble('total_earnings') ?? 0.0;
  }

  // Authentication State
  Future<bool> isLoggedIn() async {
    final token = await getAccessToken();
    final captain = await getCaptainData();
    return token != null && captain != null;
  }

  Future<bool> isCaptainApproved() async {
    final captain = await getCaptainData();
    return captain?.isApproved ?? false;
  }

  // Clear All Data
  Future<void> clearAll() async {
    await prefs.clear();
  }

  // Backup and Restore
  Future<Map<String, dynamic>> exportData() async {
    final keys = prefs.getKeys();
    final data = <String, dynamic>{};
    
    for (final key in keys) {
      final value = prefs.get(key);
      if (value != null) {
        data[key] = value;
      }
    }
    
    return data;
  }

  Future<void> importData(Map<String, dynamic> data) async {
    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;
      
      if (value is String) {
        await prefs.setString(key, value);
      } else if (value is int) {
        await prefs.setInt(key, value);
      } else if (value is double) {
        await prefs.setDouble(key, value);
      } else if (value is bool) {
        await prefs.setBool(key, value);
      } else if (value is List<String>) {
        await prefs.setStringList(key, value);
      }
    }
  }
}
