class CaptainModel {
  final String id;
  final String name;
  final String phone;
  final String email;
  final String vehicleType;
  final String vehicleNumber;
  final String? vehicleModel;
  final String? vehicleColor;
  final String status; // offline, online, busy
  final bool isApproved;
  final double rating;
  final int totalOrders;
  final int completedOrders;
  final int cancelledOrders;
  final double totalEarnings;
  final CaptainLocation? currentLocation;
  final Map<String, dynamic>? documents;
  final DateTime? approvedAt;
  final String? approvedBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  CaptainModel({
    required this.id,
    required this.name,
    required this.phone,
    required this.email,
    required this.vehicleType,
    required this.vehicleNumber,
    this.vehicleModel,
    this.vehicleColor,
    required this.status,
    required this.isApproved,
    required this.rating,
    required this.totalOrders,
    required this.completedOrders,
    required this.cancelledOrders,
    required this.totalEarnings,
    this.currentLocation,
    this.documents,
    this.approvedAt,
    this.approvedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CaptainModel.fromJson(Map<String, dynamic> json) {
    return CaptainModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      vehicleType: json['vehicle_type'] ?? '',
      vehicleNumber: json['vehicle_number'] ?? '',
      vehicleModel: json['vehicle_model'],
      vehicleColor: json['vehicle_color'],
      status: json['status'] ?? 'offline',
      isApproved: json['is_approved'] ?? false,
      rating: (json['rating'] ?? 0.0).toDouble(),
      totalOrders: json['total_orders'] ?? 0,
      completedOrders: json['completed_orders'] ?? 0,
      cancelledOrders: json['cancelled_orders'] ?? 0,
      totalEarnings: (json['total_earnings'] ?? 0.0).toDouble(),
      currentLocation: json['current_location'] != null
          ? CaptainLocation.fromJson(json['current_location'])
          : null,
      documents: json['documents'],
      approvedAt: json['approved_at'] != null
          ? DateTime.parse(json['approved_at'])
          : null,
      approvedBy: json['approved_by'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'vehicle_type': vehicleType,
      'vehicle_number': vehicleNumber,
      'vehicle_model': vehicleModel,
      'vehicle_color': vehicleColor,
      'status': status,
      'is_approved': isApproved,
      'rating': rating,
      'total_orders': totalOrders,
      'completed_orders': completedOrders,
      'cancelled_orders': cancelledOrders,
      'total_earnings': totalEarnings,
      'current_location': currentLocation?.toJson(),
      'documents': documents,
      'approved_at': approvedAt?.toIso8601String(),
      'approved_by': approvedBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  CaptainModel copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    String? vehicleType,
    String? vehicleNumber,
    String? vehicleModel,
    String? vehicleColor,
    String? status,
    bool? isApproved,
    double? rating,
    int? totalOrders,
    int? completedOrders,
    int? cancelledOrders,
    double? totalEarnings,
    CaptainLocation? currentLocation,
    Map<String, dynamic>? documents,
    DateTime? approvedAt,
    String? approvedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CaptainModel(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      vehicleType: vehicleType ?? this.vehicleType,
      vehicleNumber: vehicleNumber ?? this.vehicleNumber,
      vehicleModel: vehicleModel ?? this.vehicleModel,
      vehicleColor: vehicleColor ?? this.vehicleColor,
      status: status ?? this.status,
      isApproved: isApproved ?? this.isApproved,
      rating: rating ?? this.rating,
      totalOrders: totalOrders ?? this.totalOrders,
      completedOrders: completedOrders ?? this.completedOrders,
      cancelledOrders: cancelledOrders ?? this.cancelledOrders,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      currentLocation: currentLocation ?? this.currentLocation,
      documents: documents ?? this.documents,
      approvedAt: approvedAt ?? this.approvedAt,
      approvedBy: approvedBy ?? this.approvedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class CaptainLocation {
  final double latitude;
  final double longitude;
  final DateTime timestamp;

  CaptainLocation({
    required this.latitude,
    required this.longitude,
    required this.timestamp,
  });

  factory CaptainLocation.fromJson(Map<String, dynamic> json) {
    return CaptainLocation(
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
