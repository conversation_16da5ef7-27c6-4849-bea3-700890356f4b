const http = require('http');

const BASE_URL = 'http://localhost:3000/api';

const testAccounts = [
  {
    name: 'Customer Account',
    email: '<EMAIL>',
    password: 'Test123456',
    type: 'customer'
  },
  {
    name: 'Captain Account', 
    email: '<EMAIL>',
    password: 'Test123456',
    type: 'captain'
  },
  {
    name: 'Admin Account',
    email: '<EMAIL>', 
    password: 'password123',
    type: 'admin'
  }
];

function makeRequest(url, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(body);
          resolve({ data: jsonData, status: res.statusCode });
        } catch (e) {
          resolve({ data: body, status: res.statusCode });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testLogin(account) {
  try {
    console.log(`\n🔐 Testing login for ${account.name}...`);

    const response = await makeRequest(`${BASE_URL}/auth/login`, 'POST', {
      email: account.email,
      password: account.password
    });

    if (response.data.success) {
      console.log(`✅ ${account.name} login successful!`);
      console.log(`   User ID: ${response.data.data.user.id}`);
      console.log(`   Name: ${response.data.data.user.name}`);
      console.log(`   Type: ${response.data.data.user.userType}`);
      console.log(`   Verified: ${response.data.data.user.isVerified}`);

      return true;
    } else {
      console.log(`❌ ${account.name} login failed: ${response.data.message}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${account.name} login error: ${error.message}`);
    return false;
  }
}

async function testAllAccounts() {
  console.log('🎯 Testing all test accounts...');
  console.log('=====================================');
  
  let successCount = 0;
  
  for (const account of testAccounts) {
    const success = await testLogin(account);
    if (success) successCount++;
    
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📊 Test Results:');
  console.log('=====================================');
  console.log(`✅ Successful logins: ${successCount}/${testAccounts.length}`);
  console.log(`❌ Failed logins: ${testAccounts.length - successCount}/${testAccounts.length}`);
  
  if (successCount === testAccounts.length) {
    console.log('\n🎉 All test accounts are working perfectly!');
    console.log('You can now use these accounts to test your apps.');
  } else {
    console.log('\n⚠️ Some accounts have issues. Please check the backend server.');
  }
}

// Check if server is running first
async function checkServer() {
  try {
    await makeRequest(`${BASE_URL}/health`);
    return true;
  } catch (error) {
    console.log('❌ Backend server is not running!');
    console.log('Please start the backend server first:');
    console.log('   cd backend && npm run dev');
    return false;
  }
}

async function main() {
  console.log('🚀 Starting test accounts verification...\n');
  
  const serverRunning = await checkServer();
  if (!serverRunning) {
    process.exit(1);
  }
  
  await testAllAccounts();
}

main().catch(console.error);
