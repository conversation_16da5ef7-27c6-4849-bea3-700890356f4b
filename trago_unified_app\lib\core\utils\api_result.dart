class ApiResult<T> {
  final bool isSuccess;
  final T? data;
  final String message;
  final int? statusCode;
  final Map<String, dynamic>? errors;

  const ApiResult._({
    required this.isSuccess,
    this.data,
    required this.message,
    this.statusCode,
    this.errors,
  });

  factory ApiResult.success(T data, {String message = 'Success'}) {
    return ApiResult._(
      isSuccess: true,
      data: data,
      message: message,
      statusCode: 200,
    );
  }

  factory ApiResult.error(
    String message, {
    int? statusCode,
    Map<String, dynamic>? errors,
  }) {
    return ApiResult._(
      isSuccess: false,
      message: message,
      statusCode: statusCode,
      errors: errors,
    );
  }

  factory ApiResult.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>)? fromJsonT,
  ) {
    final success = json['success'] as bool? ?? false;
    final message = json['message'] as String? ?? '';
    final statusCode = json['status_code'] as int?;
    final errors = json['errors'] as Map<String, dynamic>?;

    if (success && json['data'] != null && fromJsonT != null) {
      final data = fromJsonT(json['data'] as Map<String, dynamic>);
      return ApiResult.success(data, message: message);
    } else if (success) {
      return ApiResult._(
        isSuccess: true,
        message: message,
        statusCode: statusCode,
      );
    } else {
      return ApiResult.error(
        message,
        statusCode: statusCode,
        errors: errors,
      );
    }
  }

  bool get isError => !isSuccess;

  @override
  String toString() {
    return 'ApiResult(isSuccess: $isSuccess, message: $message, data: $data)';
  }
}
