const knex = require('knex');

const db = knex({
  client: 'sqlite3',
  connection: {
    filename: './database/trago_dev.db'
  },
  useNullAsDefault: true
});

async function checkDatabase() {
  try {
    console.log('Checking database...');
    
    // Check if users table exists
    const hasUsersTable = await db.schema.hasTable('users');
    console.log('Users table exists:', hasUsersTable);
    
    if (hasUsersTable) {
      // Get users count
      const usersCount = await db('users').count('* as count').first();
      console.log('Users count:', usersCount.count);
      
      // Get first few users
      const users = await db('users').select('id', 'phone', 'email', 'name', 'user_type', 'is_active', 'password').limit(5);
      console.log('Sample users:', users);
    }
    
    // Check captains table
    const hasCaptainsTable = await db.schema.hasTable('captains');
    console.log('Captains table exists:', hasCaptainsTable);

    if (hasCaptainsTable) {
      const captains = await db('captains')
        .join('users', 'captains.user_id', 'users.id')
        .select('captains.*', 'users.name', 'users.email', 'users.phone')
        .limit(5);
      console.log('Sample captains:', captains);
    }

    // Check tables
    const tables = await db.raw("SELECT name FROM sqlite_master WHERE type='table'");
    console.log('Available tables:', tables.map(t => t.name));
    
  } catch (error) {
    console.error('Database error:', error);
  } finally {
    await db.destroy();
  }
}

checkDatabase();
