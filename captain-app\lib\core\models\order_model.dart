class OrderModel {
  final String id;
  final String userId;
  final String storeId;
  final String? captainId;
  final String status;
  final double totalAmount;
  final double deliveryFee;
  final String paymentMethod;
  final String deliveryAddress;
  final OrderLocation deliveryLocation;
  final DateTime? estimatedDelivery;
  final List<OrderItem> items;
  final CustomerInfo customer;
  final StoreInfo store;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  OrderModel({
    required this.id,
    required this.userId,
    required this.storeId,
    this.captainId,
    required this.status,
    required this.totalAmount,
    required this.deliveryFee,
    required this.paymentMethod,
    required this.deliveryAddress,
    required this.deliveryLocation,
    this.estimatedDelivery,
    required this.items,
    required this.customer,
    required this.store,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      storeId: json['store_id'] ?? '',
      captainId: json['captain_id'],
      status: json['status'] ?? 'pending',
      totalAmount: (json['total_amount'] ?? 0.0).toDouble(),
      deliveryFee: (json['delivery_fee'] ?? 0.0).toDouble(),
      paymentMethod: json['payment_method'] ?? 'cash',
      deliveryAddress: json['delivery_address'] ?? '',
      deliveryLocation: OrderLocation.fromJson(json['delivery_location'] ?? {}),
      estimatedDelivery: json['estimated_delivery'] != null
          ? DateTime.parse(json['estimated_delivery'])
          : null,
      items: (json['items'] as List<dynamic>?)
              ?.map((item) => OrderItem.fromJson(item))
              .toList() ??
          [],
      customer: CustomerInfo.fromJson(json['customer'] ?? {}),
      store: StoreInfo.fromJson(json['store'] ?? {}),
      notes: json['notes'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'store_id': storeId,
      'captain_id': captainId,
      'status': status,
      'total_amount': totalAmount,
      'delivery_fee': deliveryFee,
      'payment_method': paymentMethod,
      'delivery_address': deliveryAddress,
      'delivery_location': deliveryLocation.toJson(),
      'estimated_delivery': estimatedDelivery?.toIso8601String(),
      'items': items.map((item) => item.toJson()).toList(),
      'customer': customer.toJson(),
      'store': store.toJson(),
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  double get totalWithDelivery => totalAmount + deliveryFee;

  bool get canAccept => status == 'pending' || status == 'confirmed';
  bool get canPickup => status == 'ready';
  bool get canDeliver => status == 'picked_up' || status == 'on_the_way';
  bool get isCompleted => status == 'delivered';
  bool get isCancelled => status == 'cancelled';
}

class OrderItem {
  final String id;
  final String productId;
  final String name;
  final String? image;
  final double price;
  final int quantity;
  final String? notes;

  OrderItem({
    required this.id,
    required this.productId,
    required this.name,
    this.image,
    required this.price,
    required this.quantity,
    this.notes,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      id: json['id'] ?? '',
      productId: json['product_id'] ?? '',
      name: json['name'] ?? '',
      image: json['image'],
      price: (json['price'] ?? 0.0).toDouble(),
      quantity: json['quantity'] ?? 1,
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'name': name,
      'image': image,
      'price': price,
      'quantity': quantity,
      'notes': notes,
    };
  }

  double get totalPrice => price * quantity;
}

class OrderLocation {
  final double latitude;
  final double longitude;

  OrderLocation({
    required this.latitude,
    required this.longitude,
  });

  factory OrderLocation.fromJson(Map<String, dynamic> json) {
    return OrderLocation(
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

class CustomerInfo {
  final String id;
  final String name;
  final String phone;
  final String? avatar;

  CustomerInfo({
    required this.id,
    required this.name,
    required this.phone,
    this.avatar,
  });

  factory CustomerInfo.fromJson(Map<String, dynamic> json) {
    return CustomerInfo(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      avatar: json['avatar'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'avatar': avatar,
    };
  }
}

class StoreInfo {
  final String id;
  final String name;
  final String address;
  final String phone;
  final String? image;
  final OrderLocation location;

  StoreInfo({
    required this.id,
    required this.name,
    required this.address,
    required this.phone,
    this.image,
    required this.location,
  });

  factory StoreInfo.fromJson(Map<String, dynamic> json) {
    return StoreInfo(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      phone: json['phone'] ?? '',
      image: json['image'],
      location: OrderLocation.fromJson(json['location'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'phone': phone,
      'image': image,
      'location': location.toJson(),
    };
  }
}
