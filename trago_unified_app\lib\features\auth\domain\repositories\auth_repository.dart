import '../../../../core/models/user_model.dart';
import '../../../../core/utils/api_result.dart';

abstract class AuthRepositoryInterface {
  Future<ApiResult<UserModel>> login({
    required String phone,
    required String password,
    required String userType,
  });

  Future<ApiResult<void>> register({
    required String name,
    required String phone,
    required String email,
    required String password,
    required String userType,
    Map<String, dynamic>? additionalData,
  });

  Future<ApiResult<UserModel>> verifyOTP({
    required String phone,
    required String otp,
    required String userType,
  });

  Future<ApiResult<void>> resendOTP({
    required String phone,
    required String userType,
  });

  Future<ApiResult<void>> forgotPassword({
    required String phone,
    required String userType,
  });

  Future<ApiResult<void>> resetPassword({
    required String phone,
    required String otp,
    required String newPassword,
    required String userType,
  });

  Future<ApiResult<UserModel>> updateProfile({
    required String name,
    String? email,
    String? avatar,
  });

  Future<ApiResult<void>> logout();
}
