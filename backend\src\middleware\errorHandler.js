const logger = require('../utils/logger');

const errorHandler = (err, req, res, next) => {
  // Log error
  logger.logError(err, req);

  let error = { ...err };
  error.message = err.message;

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Resource not found';
    const message_ar = 'المورد غير موجود';
    error = { message, message_ar, statusCode: 404 };
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = 'Duplicate field value entered';
    const message_ar = 'تم إدخال قيمة مكررة';
    error = { message, message_ar, statusCode: 400 };
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message).join(', ');
    const message_ar = 'خطأ في التحقق من البيانات';
    error = { message, message_ar, statusCode: 400 };
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    const message = 'Invalid token';
    const message_ar = 'رمز مصادقة غير صحيح';
    error = { message, message_ar, statusCode: 401 };
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'Token expired';
    const message_ar = 'انتهت صلاحية رمز المصادقة';
    error = { message, message_ar, statusCode: 401 };
  }

  // Database errors
  if (err.code === '23505') { // PostgreSQL unique violation
    const message = 'Duplicate entry';
    const message_ar = 'إدخال مكرر';
    error = { message, message_ar, statusCode: 400 };
  }

  if (err.code === '23503') { // PostgreSQL foreign key violation
    const message = 'Referenced record not found';
    const message_ar = 'السجل المرجعي غير موجود';
    error = { message, message_ar, statusCode: 400 };
  }

  if (err.code === '23502') { // PostgreSQL not null violation
    const message = 'Required field missing';
    const message_ar = 'حقل مطلوب مفقود';
    error = { message, message_ar, statusCode: 400 };
  }

  // File upload errors
  if (err.code === 'LIMIT_FILE_SIZE') {
    const message = 'File too large';
    const message_ar = 'الملف كبير جداً';
    error = { message, message_ar, statusCode: 400 };
  }

  if (err.code === 'LIMIT_FILE_COUNT') {
    const message = 'Too many files';
    const message_ar = 'عدد كبير جداً من الملفات';
    error = { message, message_ar, statusCode: 400 };
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    const message = 'Unexpected file field';
    const message_ar = 'حقل ملف غير متوقع';
    error = { message, message_ar, statusCode: 400 };
  }

  // Rate limiting errors
  if (err.status === 429) {
    const message = 'Too many requests, please try again later';
    const message_ar = 'عدد كبير جداً من الطلبات، يرجى المحاولة لاحقاً';
    error = { message, message_ar, statusCode: 429 };
  }

  // Payment gateway errors
  if (err.code === 'PAYMENT_FAILED') {
    const message = 'Payment processing failed';
    const message_ar = 'فشل في معالجة الدفع';
    error = { message, message_ar, statusCode: 400 };
  }

  if (err.code === 'PAYMENT_DECLINED') {
    const message = 'Payment was declined';
    const message_ar = 'تم رفض الدفع';
    error = { message, message_ar, statusCode: 400 };
  }

  // SMS/Email service errors
  if (err.code === 'SMS_FAILED') {
    const message = 'Failed to send SMS';
    const message_ar = 'فشل في إرسال الرسالة النصية';
    error = { message, message_ar, statusCode: 500 };
  }

  if (err.code === 'EMAIL_FAILED') {
    const message = 'Failed to send email';
    const message_ar = 'فشل في إرسال البريد الإلكتروني';
    error = { message, message_ar, statusCode: 500 };
  }

  // Location/Maps errors
  if (err.code === 'LOCATION_NOT_FOUND') {
    const message = 'Location not found';
    const message_ar = 'الموقع غير موجود';
    error = { message, message_ar, statusCode: 404 };
  }

  if (err.code === 'DISTANCE_TOO_FAR') {
    const message = 'Delivery location is too far';
    const message_ar = 'موقع التوصيل بعيد جداً';
    error = { message, message_ar, statusCode: 400 };
  }

  // Business logic errors
  if (err.code === 'ORDER_NOT_FOUND') {
    const message = 'Order not found';
    const message_ar = 'الطلب غير موجود';
    error = { message, message_ar, statusCode: 404 };
  }

  if (err.code === 'STORE_CLOSED') {
    const message = 'Store is currently closed';
    const message_ar = 'المتجر مغلق حالياً';
    error = { message, message_ar, statusCode: 400 };
  }

  if (err.code === 'INSUFFICIENT_STOCK') {
    const message = 'Insufficient stock';
    const message_ar = 'المخزون غير كافي';
    error = { message, message_ar, statusCode: 400 };
  }

  if (err.code === 'CAPTAIN_NOT_AVAILABLE') {
    const message = 'No captain available';
    const message_ar = 'لا يوجد كابتن متاح';
    error = { message, message_ar, statusCode: 400 };
  }

  // Default error response
  const statusCode = error.statusCode || 500;
  const message = error.message || 'Server Error';
  const message_ar = error.message_ar || 'خطأ في الخادم';

  // Don't leak error details in production
  const response = {
    success: false,
    message,
    message_ar
  };

  // Add error details in development
  if (process.env.NODE_ENV === 'development') {
    response.error = error;
    response.stack = err.stack;
  }

  // Add request ID for tracking
  if (req.requestId) {
    response.requestId = req.requestId;
  }

  res.status(statusCode).json(response);
};

module.exports = errorHandler;
