import 'package:flutter/material.dart';

class AppConstants {
  // App Information
  static const String appName = 'Trago';
  static const String appNameArabic = 'تراجو';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق توصيل الطلبات';
  static const String appDescriptionEn = 'Food & Store Delivery App';

  // API Configuration
  static const String baseUrl = 'https://api.trago-app.com';
  static const String apiVersion = 'v1';
  static const String apiUrl = '$baseUrl/api';
  
  // Socket Configuration
  static const String socketUrl = 'https://api.trago-app.com';
  
  // Supported Languages
  static const List<Locale> supportedLocales = [
    Locale('ar'),
    Locale('en'),
  ];
  
  // Default Values
  static const Locale defaultLocale = Locale('ar');
  static const double defaultDeliveryFee = 15.0;
  static const double maxDeliveryDistance = 20.0; // km
  static const int deliveryTimeEstimate = 30; // minutes
  static const int paginationLimit = 20;
  static const double searchRadius = 10.0; // km
  static const double mapZoomLevel = 15.0;
  
  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // Timeouts
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // Cache Durations
  static const Duration shortCacheDuration = Duration(minutes: 5);
  static const Duration mediumCacheDuration = Duration(minutes: 30);
  static const Duration longCacheDuration = Duration(hours: 24);
  
  // Validation Rules
  static const int phoneMinLength = 11;
  static const int phoneMaxLength = 11;
  static const int passwordMinLength = 8;
  static const int passwordMaxLength = 50;
  static const int nameMinLength = 2;
  static const int nameMaxLength = 50;
  static const int otpLength = 6;
  
  // Regular Expressions
  static const String phoneRegex = r'^01[0-2,5]{1}[0-9]{8}$';
  static const String emailRegex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String passwordRegex = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$';
  static const String otpRegex = r'^\d{6}$';
  
  // File Upload Limits
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int maxImagesCount = 5;
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  
  // Map Configuration
  static const double defaultLatitude = 30.0444; // Cairo
  static const double defaultLongitude = 31.2357; // Cairo
  static const double minZoomLevel = 10.0;
  static const double maxZoomLevel = 20.0;
  
  // Order Configuration
  static const double minOrderAmount = 10.0;
  static const double maxOrderAmount = 1000.0;
  static const int maxOrderItems = 50;
  
  // Rating Configuration
  static const int minRating = 1;
  static const int maxRating = 5;
  static const double defaultRating = 0.0;
  
  // Notification Configuration
  static const String notificationChannelId = 'trago_notifications';
  static const String notificationChannelName = 'Trago Notifications';
  static const String notificationChannelDescription = 'Notifications for Trago app';
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String languageKey = 'language';
  static const String themeKey = 'theme';
  static const String onboardingKey = 'onboarding_completed';
  static const String locationPermissionKey = 'location_permission';
  static const String notificationPermissionKey = 'notification_permission';
  
  // Error Messages
  static const String networkErrorMessage = 'خطأ في الاتصال بالإنترنت';
  static const String networkErrorMessageEn = 'Network connection error';
  static const String serverErrorMessage = 'خطأ في الخادم';
  static const String serverErrorMessageEn = 'Server error';
  static const String unknownErrorMessage = 'حدث خطأ غير متوقع';
  static const String unknownErrorMessageEn = 'An unexpected error occurred';
  
  // Success Messages
  static const String successMessage = 'تم بنجاح';
  static const String successMessageEn = 'Success';
  
  // Loading Messages
  static const String loadingMessage = 'جاري التحميل...';
  static const String loadingMessageEn = 'Loading...';
  
  // Empty State Messages
  static const String noDataMessage = 'لا توجد بيانات';
  static const String noDataMessageEn = 'No data available';
  static const String noResultsMessage = 'لا توجد نتائج';
  static const String noResultsMessageEn = 'No results found';
  
  // Permission Messages
  static const String locationPermissionMessage = 'يحتاج التطبيق إلى إذن الموقع';
  static const String locationPermissionMessageEn = 'App needs location permission';
  static const String notificationPermissionMessage = 'يحتاج التطبيق إلى إذن الإشعارات';
  static const String notificationPermissionMessageEn = 'App needs notification permission';
  
  // Social Media Links
  static const String facebookUrl = 'https://facebook.com/trago';
  static const String twitterUrl = 'https://twitter.com/trago';
  static const String instagramUrl = 'https://instagram.com/trago';
  static const String whatsappUrl = 'https://wa.me/201234567890';
  
  // Support Information
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+201234567890';
  static const String privacyPolicyUrl = 'https://trago-app.com/privacy';
  static const String termsOfServiceUrl = 'https://trago-app.com/terms';
  
  // App Store Links
  static const String playStoreUrl = 'https://play.google.com/store/apps/details?id=com.trago.userapp';
  static const String appStoreUrl = 'https://apps.apple.com/app/trago/id123456789';
  
  // Feature Flags
  static const bool enableBiometricAuth = true;
  static const bool enableDarkMode = true;
  static const bool enableNotifications = true;
  static const bool enableLocationTracking = true;
  static const bool enableCrashReporting = true;
  static const bool enableAnalytics = true;
}
